<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">

    <!-- SEO优化 Meta标签 -->
    <title>GoMyHire 移动端快速透视分析 - 数据分析工具</title>
    <meta name="description" content="GoMyHire移动端透视分析工具，支持CSV和Excel文件上传，快速生成透视表分析结果，提供智能字段映射和数据可视化功能。">
    <meta name="keywords" content="透视表,数据分析,Excel,CSV,移动端,数据可视化,GoMyHire,字段映射,聚合分析">
    <meta name="author" content="GoMyHire Team">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#007aff">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://gomyhire.com/pivot-analysis">
    <meta property="og:title" content="GoMyHire 移动端快速透视分析">
    <meta property="og:description" content="专业的移动端数据透视分析工具，支持多种文件格式，提供智能数据处理和可视化功能。">
    <meta property="og:image" content="src/assets/icons/og-image.png">
    <meta property="og:locale" content="zh_CN">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://gomyhire.com/pivot-analysis">
    <meta property="twitter:title" content="GoMyHire 移动端快速透视分析">
    <meta property="twitter:description" content="专业的移动端数据透视分析工具，支持多种文件格式，提供智能数据处理和可视化功能。">
    <meta property="twitter:image" content="src/assets/icons/twitter-image.png">

    <!-- iOS PWA优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="GoMyHire透视分析">
    <meta name="format-detection" content="telephone=no">

    <!-- 安全性和性能优化 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="referrer" content="strict-origin-when-cross-origin">

    <!-- 资源预加载优化 -->
    <link rel="preload" href="src/css/main.css" as="style">
    <link rel="preload" href="src/js/core/smartoffice-core.js" as="script">
    <link rel="preload" href="src/js/core/smartoffice-app.js" as="script">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="preconnect" href="//fonts.gstatic.com" crossorigin>
    
    <!-- 关键CSS内联优化 -->
    <style>
        /* 关键渲染路径CSS - 内联以减少阻塞 */
        :root{--ios-blue:#007AFF;--ios-green:#34C759;--ios-red:#FF3B30;--background-primary:#F2F2F7;--background-secondary:#FFFFFF;--text-primary:#000000;--text-secondary:#8E8E93;--spacing-md:16px;--radius-medium:12px;--animation-fast:0.2s}
        *{box-sizing:border-box;margin:0;padding:0}
        body{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background-color:var(--background-primary);color:var(--text-primary);overflow-x:hidden}
        .app-container{min-height:100vh;display:flex;flex-direction:column}
        .nav-bar{position:fixed;top:0;left:0;right:0;height:64px;background:rgba(255,255,255,0.95);backdrop-filter:blur(20px);z-index:10000}
        .main-content{flex:1;margin-top:64px}
        .page{position:absolute;top:0;left:0;right:0;bottom:0;background:var(--background-primary);overflow-y:auto}
        .page-hidden{transform:translateX(100%)}
        .loading-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;z-index:9999}
        .loading-overlay-hidden{display:none}
    </style>

    <!-- iOS风格CSS文件 - 按优先级顺序加载 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/ios-theme.css">
    <link rel="stylesheet" href="src/css/mobile.css">

    <!-- 组件样式文件 -->
    <link rel="stylesheet" href="src/css/components/charts.css">
    <link rel="stylesheet" href="src/css/components/templates.css">
    <link rel="stylesheet" href="src/css/components/filters.css">
    <link rel="stylesheet" href="src/css/components/config-list.css">
    <link rel="stylesheet" href="src/css/components/config-form.css">
    <link rel="stylesheet" href="src/css/components/config-form-integration.css">
    <link rel="stylesheet" href="src/css/components/file-upload.css">
    <link rel="stylesheet" href="src/css/components/file-upload-page.css">
    <link rel="stylesheet" href="src/css/components/dropdown.css">
    <link rel="stylesheet" href="src/css/components/field-selector.css">
    <link rel="stylesheet" href="src/css/components/data-table.css">
    <link rel="stylesheet" href="src/css/components/toast.css">
    <link rel="stylesheet" href="src/css/components/field-mapping-page.css">  <!-- 字段映射管理页面样式 -->
    <link rel="stylesheet" href="src/css/components/mapping-dialog.css">  <!-- 映射配置对话框样式 -->
    <link rel="stylesheet" href="src/css/components/navigation-bar-enhanced.css">  <!-- 重构后的导航栏样式 -->
    <link rel="stylesheet" href="src/css/components/timerange-dialog.css">  <!-- 时间段配置对话框样式 -->
    <link rel="stylesheet" href="src/css/components/aggregation-panel.css">  <!-- 聚合模式管理面板样式 -->
    
    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="src/assets/icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="src/assets/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="src/assets/icons/favicon-16x16.png">
</head>
<body>
    <!-- iOS风格状态栏占位 -->
    <div class="status-bar-spacer"></div>
    
    <!-- 主应用容器 -->
    <div id="app" class="app-container" role="application" aria-label="GoMyHire透视分析应用">
        <!-- 跳转到主内容的无障碍链接 -->
        <a href="#mainContent" class="skip-to-content" aria-label="跳转到主内容">跳转到主内容</a>

        <!-- 重构后的导航栏 - 将由NavigationBarComponent动态生成 -->
        <header class="nav-bar" id="navigationBar" role="banner" aria-label="主导航">
            <!-- 导航栏内容将由NavigationBarComponent动态生成 -->
        </header>

        <!-- 主内容区域 -->
        <main class="main-content" id="mainContent" role="main" aria-label="主要内容区域">
            <!-- 文件上传主页 -->
            <section class="page" id="fileUploadPage" role="tabpanel" aria-labelledby="fileUploadPageTitle">
                <div class="page-content">
                    <!-- 欢迎区域 -->
                    <header class="welcome-section" role="banner">
                        <div class="welcome-icon" aria-hidden="true">
                            <svg viewBox="0 0 24 24" focusable="false">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                        </div>
                        <h1 class="welcome-title" id="fileUploadPageTitle">GoMyHire 透视分析</h1>
                        <p class="welcome-description">上传您的数据文件，快速生成透视表分析结果</p>
                    </header>

                    <!-- 文件上传区域 -->
                    <section class="file-upload-section" aria-labelledby="fileUploadSectionTitle">
                        <h2 id="fileUploadSectionTitle" class="sr-only">文件上传区域</h2>
                        <div id="fileUploadContainer" role="region" aria-label="文件上传组件">
                            <!-- 文件上传组件将在这里初始化 -->
                        </div>
                    </section>

                    <!-- 已保存配置区域 -->
                    <section class="saved-configs-section" id="savedConfigsSection" aria-labelledby="savedConfigsSectionTitle">
                        <header class="section-header">
                            <h2 class="section-title" id="savedConfigsSectionTitle">已保存的透视表配置</h2>
                            <button type="button"
                                    class="section-action-btn"
                                    id="manageConfigsBtn"
                                    aria-label="管理透视表配置"
                                    title="管理配置">
                                <svg viewBox="0 0 24 24" aria-hidden="true" focusable="false">
                                    <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                                </svg>
                            </button>
                        </header>

                        <!-- 配置列表预览 -->
                        <div class="config-preview-list"
                             id="configPreviewList"
                             role="list"
                             aria-label="透视表配置预览列表">
                            <!-- 配置预览卡片将在这里动态生成 -->
                        </div>

                        <!-- 空状态提示 -->
                        <div class="empty-configs-state"
                             id="emptyConfigsState"
                             role="status"
                             aria-live="polite"
                             aria-label="空状态提示">
                            <div class="empty-icon" aria-hidden="true">
                                <svg viewBox="0 0 24 24" focusable="false">
                                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                                </svg>
                            </div>
                            <p class="empty-description">还没有保存的透视表配置<br>上传文件后可以创建配置</p>
                        </div>
                    </section>
                </div>
            </div>

            <!-- 配置管理页面 -->
            <section class="page page-hidden"
                     id="configListPage"
                     role="tabpanel"
                     aria-labelledby="configListPageTitle"
                     aria-hidden="true">
                <div class="page-content">
                    <h1 id="configListPageTitle" class="sr-only">透视表配置管理</h1>

                    <!-- 空状态提示 -->
                    <div class="empty-state"
                         id="emptyState"
                         role="status"
                         aria-live="polite"
                         aria-label="空状态提示">
                        <div class="empty-icon" aria-hidden="true">
                            <svg viewBox="0 0 24 24" focusable="false">
                                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                            </svg>
                        </div>
                        <h2 class="empty-title">还没有透视表配置</h2>
                        <p class="empty-description">点击右上角的 + 按钮创建您的第一个透视表配置</p>
                    </div>

                    <!-- 配置列表容器 -->
                    <div class="config-list"
                         id="configList"
                         role="list"
                         aria-label="透视表配置列表">
                        <!-- 配置卡片将在这里动态生成 -->
                    </div>
                </div>
            </section>

            <!-- 配置表单页面 -->
            <section class="page page-hidden"
                     id="configFormPage"
                     role="tabpanel"
                     aria-labelledby="configFormPageTitle"
                     aria-hidden="true">
                <div class="page-content">
                    <h1 id="configFormPageTitle" class="sr-only">透视表配置表单</h1>
                    <!-- 表单内容将由ConfigFormComponent动态生成 -->
                </div>
            </section>

            <!-- 字段映射管理页面 -->
            <section class="page page-hidden"
                     id="fieldMappingPage"
                     role="tabpanel"
                     aria-labelledby="fieldMappingPageTitle"
                     aria-hidden="true">
                <div class="page-content">
                    <h1 id="fieldMappingPageTitle" class="sr-only">字段映射管理</h1>
                    <!-- 字段映射管理内容将由FieldMappingPageComponent动态生成 -->
                </div>
            </section>
        </main>
        
        <!-- iOS风格加载指示器 -->
        <div class="loading-overlay loading-overlay-hidden"
             id="loadingOverlay"
             role="dialog"
             aria-modal="true"
             aria-label="加载中"
             aria-live="assertive">
            <div class="loading-spinner" aria-hidden="true">
                <div class="spinner-ring"></div>
            </div>
            <span class="sr-only">正在加载，请稍候...</span>
        </div>

        <!-- iOS风格提示框容器 -->
        <div class="toast-container"
             id="toastContainer"
             role="region"
             aria-live="polite"
             aria-label="通知消息区域"></div>
    </div>
    
    <!-- JavaScript文件 - 按依赖顺序加载 -->

    <!-- 0. 零依赖架构 - 无第三方库 -->

    <!-- 1. 核心基础设施 -->
    <script src="src/js/core/smartoffice-core.js"></script>
    <script src="src/js/core/smartoffice-events.js"></script>
    <script src="src/js/core/smartoffice-storage.js"></script>
    <script src="src/js/core/smartoffice-router.js"></script>
    <script src="src/js/core/smartoffice-offline.js"></script>
    <script src="src/js/core/smartoffice-error-handler.js"></script>
    
    <!-- 2. 工具函数 -->
    <script src="src/js/utils/smartoffice-helpers.js"></script>
    <script src="src/js/utils/smartoffice-dom.js"></script>
    <script src="src/js/utils/smartoffice-format.js"></script>
    
    <!-- 3. 数据处理模块 -->
    <script src="src/js/data/smartoffice-data-schemas.js"></script>  <!-- 数据结构定义 -->
    <script src="src/js/data/smartoffice-data-validator.js"></script>
    <script src="src/js/data/smartoffice-csv-parser.js"></script>
    <script src="src/js/data/smartoffice-config-manager.js"></script>  <!-- 修复路径 -->
    <script src="src/js/data/smartoffice-pivot-engine.js"></script>
    <script src="src/js/data/smartoffice-field-mapper.js"></script>  <!-- 字段映射管理器 -->
    <script src="src/js/data/smartoffice-timerange-manager.js"></script>  <!-- 时间段管理器 -->
    <script src="src/js/data/smartoffice-aggregation-manager.js"></script>  <!-- 聚合模式管理器 -->

    <!-- 3.1 文件解析器 -->
    <script src="src/js/parsers/smartoffice-excel-parser.js"></script>
    <script src="src/js/parsers/smartoffice-json-parser.js"></script>

    <!-- 3.2 模板管理 -->
    <script src="src/js/templates/smartoffice-template-manager.js"></script>

    <!-- 3.3 数据筛选 -->
    <script src="src/js/filters/smartoffice-data-filters.js"></script>

    <!-- 3.4 可视化模块 -->
    <script src="src/js/visualization/smartoffice-formatter.js"></script>
    <script src="src/js/visualization/smartoffice-chart-engine.js"></script>
    
    <!-- 4. UI组件 -->
    <script src="src/js/components/smartoffice-loading.js"></script>
    <script src="src/js/components/smartoffice-toast.js"></script>
    <script src="src/js/components/smartoffice-dropdown.js"></script>
    <script src="src/js/components/smartoffice-field-selector.js"></script>
    <script src="src/js/components/smartoffice-file-upload.js"></script>
    <script src="src/js/components/smartoffice-data-preview.js"></script>
    <script src="src/js/components/smartoffice-data-table.js"></script>
    <script src="src/js/components/smartoffice-config-form.js"></script>
    <script src="src/js/components/smartoffice-config-list.js"></script>
    <script src="src/js/components/smartoffice-config-preview.js"></script>
    <script src="src/js/components/smartoffice-field-mapping-page.js"></script>  <!-- 字段映射管理页面 -->
    <script src="src/js/components/smartoffice-mapping-dialog.js"></script>  <!-- 映射配置对话框 -->
    <script src="src/js/components/smartoffice-navigation-bar.js"></script>  <!-- 重构后的导航栏组件 -->
    <script src="src/js/components/smartoffice-timerange-dialog.js"></script>  <!-- 时间段配置对话框组件 -->
    
    <!-- 5. 应用主控制器 -->
    <script src="src/js/core/smartoffice-app.js"></script>
    
    <!-- 6. 应用启动 -->
    <script>
        /**
         * @function Service Worker注册
         * 注册Service Worker以支持离线功能
         */
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('src/js/workers/smartoffice-service-worker.js')
                    .then(function(registration) {
                        console.log('✅ Service Worker注册成功:', registration.scope);

                        // 监听Service Worker更新
                        registration.addEventListener('updatefound', function() {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', function() {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // 新版本可用，提示用户刷新
                                    if (SmartOffice && SmartOffice.Core && SmartOffice.Core.EventBus) {
                                        SmartOffice.Core.EventBus.emit('app:updateAvailable');
                                    }
                                }
                            });
                        });
                    })
                    .catch(function(error) {
                        console.log('❌ Service Worker注册失败:', error);
                    });
            });
        }

        /**
         * @function 应用初始化
         * 在DOM加载完成后启动SmartOffice应用
         */
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 检查关键文件是否加载成功
                const requiredComponents = [
                    'SmartOffice',
                    'SmartOffice.Core',
                    'SmartOffice.Core.App'
                ];
                
                for (const component of requiredComponents) {
                    const parts = component.split('.');
                    let obj = window;
                    for (const part of parts) {
                        if (!obj[part]) {
                            throw new Error(`必需组件 ${component} 未正确加载`);
                        }
                        obj = obj[part];
                    }
                }
                
                // 初始化应用
                SmartOffice.Core.App.init();
                
                // 开发模式下的调试信息
                if (SmartOffice.Config && SmartOffice.Config.DEBUG) {
                    console.log('🚀 GoMyHire移动端快速透视分析应用启动成功');
                    console.log('📱 iOS风格界面已加载');
                    console.log('🔧 传统JavaScript架构运行正常');
                }
            } catch (error) {
                console.error('❌ 应用启动失败:', error);
                // 显示用户友好的错误提示
                document.body.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #ff3b30; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        <h2 style="margin-bottom: 16px;">应用启动失败</h2>
                        <p style="margin-bottom: 12px;">请检查网络连接后刷新页面重试</p>
                        <p style="font-size: 12px; color: #8e8e93; margin-bottom: 20px;">${error.message}</p>
                        <button onclick="location.reload()" style="background: #007aff; color: white; border: none; padding: 8px 16px; border-radius: 8px; font-size: 16px;">
                            重新加载
                        </button>
                    </div>
                `;
            }
        });
        
        /**
         * @function 处理iOS Safari的特殊情况
         * 确保在iOS设备上的最佳体验
         */
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
            // 防止iOS Safari的橡皮筋效果
            document.addEventListener('touchmove', function(e) {
                if (e.target.closest('.scrollable')) {
                    return; // 允许可滚动区域的滚动
                }
                e.preventDefault();
            }, { passive: false });
            
            // 处理iOS Safari的视口高度问题
            function updateViewportHeight() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', vh + 'px');
            }
            
            updateViewportHeight();
            window.addEventListener('resize', updateViewportHeight);
            window.addEventListener('orientationchange', function() {
                setTimeout(updateViewportHeight, 100);
            });
        }
    </script>
</body>
</html>
