/**
 * @file SmartOffice Toast通知组件
 * @description iOS风格的Toast通知组件，支持多种类型和自动消失
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function ToastComponent
 * @description Toast通知组件构造函数
 * @constructor
 */
function ToastComponent() {
    /**
     * @property {Object} config - Toast配置
     */
    this.config = {
        duration: 3000,
        position: 'top',
        maxToasts: 3,
        animationDuration: 300
    };

    /**
     * @property {Array} activeToasts - 当前活动的Toast列表
     */
    this.activeToasts = [];

    /**
     * @property {Element} container - Toast容器元素
     */
    this.container = null;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    this.init();
    SmartOffice.log('info', 'ToastComponent Toast通知组件初始化完成');
}

/**
 * @function ToastComponent.prototype.init
 * @description 初始化Toast组件
 */
ToastComponent.prototype.init = function() {
    this.createContainer();
    this.bindEvents();
};

/**
 * @function ToastComponent.prototype.createContainer
 * @description 创建Toast容器
 */
ToastComponent.prototype.createContainer = function() {
    // 检查是否已存在容器
    this.container = document.getElementById('toastContainer');
    
    if (!this.container) {
        this.container = document.createElement('div');
        this.container.id = 'toastContainer';
        this.container.className = 'toast-container';
        
        // 设置容器样式
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
            max-width: 320px;
        `;
        
        document.body.appendChild(this.container);
    }
};

/**
 * @function ToastComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
ToastComponent.prototype.bindEvents = function() {
    const self = this;
    
    // 监听Toast显示事件
    this.eventBus.on(SmartOffice.Events.UI_TOAST_SHOW, function(data) {
        if (typeof data === 'string') {
            self.show(data, 'info');
        } else {
            self.show(data.message, data.type, data.duration);
        }
    });
};

/**
 * @function ToastComponent.prototype.show
 * @description 显示Toast通知
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('success', 'error', 'warning', 'info')
 * @param {number} duration - 显示时长（毫秒）
 */
ToastComponent.prototype.show = function(message, type, duration) {
    type = type || 'info';
    duration = duration || this.config.duration;

    // 限制Toast数量
    if (this.activeToasts.length >= this.config.maxToasts) {
        this.removeOldestToast();
    }

    // 创建Toast元素
    const toast = this.createToastElement(message, type);
    
    // 添加到容器
    this.container.appendChild(toast);
    this.activeToasts.push(toast);

    // 显示动画
    this.showToast(toast);

    // 自动隐藏
    if (duration > 0) {
        setTimeout(() => {
            this.hide(toast);
        }, duration);
    }

    SmartOffice.log('info', 'Toast显示:', message, type);
};

/**
 * @function ToastComponent.prototype.createToastElement
 * @description 创建Toast元素
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型
 * @returns {Element} Toast元素
 */
ToastComponent.prototype.createToastElement = function(message, type) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    // 获取类型对应的图标和颜色
    const typeConfig = this.getTypeConfig(type);
    
    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-icon">${typeConfig.icon}</span>
            <span class="toast-message">${this.escapeHtml(message)}</span>
            <button class="toast-close" type="button">&times;</button>
        </div>
    `;

    // 设置样式
    toast.style.cssText = `
        background: ${typeConfig.background};
        color: ${typeConfig.color};
        border: 1px solid ${typeConfig.border};
        border-radius: 12px;
        padding: 12px 16px;
        margin-bottom: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        font-size: 14px;
        line-height: 1.4;
        pointer-events: auto;
        transform: translateX(100%);
        opacity: 0;
        transition: all ${this.config.animationDuration}ms ease;
        max-width: 100%;
        word-wrap: break-word;
    `;

    // 内容样式
    const content = toast.querySelector('.toast-content');
    content.style.cssText = `
        display: flex;
        align-items: center;
        gap: 8px;
    `;

    // 图标样式
    const icon = toast.querySelector('.toast-icon');
    icon.style.cssText = `
        flex-shrink: 0;
        font-size: 16px;
    `;

    // 消息样式
    const messageEl = toast.querySelector('.toast-message');
    messageEl.style.cssText = `
        flex: 1;
        min-width: 0;
    `;

    // 关闭按钮样式
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.style.cssText = `
        background: none;
        border: none;
        color: inherit;
        font-size: 18px;
        line-height: 1;
        padding: 0;
        margin: 0;
        cursor: pointer;
        opacity: 0.7;
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    // 绑定关闭事件
    const self = this;
    closeBtn.addEventListener('click', function() {
        self.hide(toast);
    });

    return toast;
};

/**
 * @function ToastComponent.prototype.getTypeConfig
 * @description 获取类型配置
 * @param {string} type - 消息类型
 * @returns {Object} 类型配置
 */
ToastComponent.prototype.getTypeConfig = function(type) {
    const configs = {
        success: {
            icon: '✅',
            background: 'rgba(52, 199, 89, 0.9)',
            color: '#FFFFFF',
            border: 'rgba(52, 199, 89, 0.3)'
        },
        error: {
            icon: '❌',
            background: 'rgba(255, 59, 48, 0.9)',
            color: '#FFFFFF',
            border: 'rgba(255, 59, 48, 0.3)'
        },
        warning: {
            icon: '⚠️',
            background: 'rgba(255, 149, 0, 0.9)',
            color: '#FFFFFF',
            border: 'rgba(255, 149, 0, 0.3)'
        },
        info: {
            icon: 'ℹ️',
            background: 'rgba(0, 122, 255, 0.9)',
            color: '#FFFFFF',
            border: 'rgba(0, 122, 255, 0.3)'
        }
    };

    return configs[type] || configs.info;
};

/**
 * @function ToastComponent.prototype.showToast
 * @description 显示Toast动画
 * @param {Element} toast - Toast元素
 */
ToastComponent.prototype.showToast = function(toast) {
    // 强制重排以确保初始样式生效
    toast.offsetHeight;
    
    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
        toast.style.opacity = '1';
    }, 10);
};

/**
 * @function ToastComponent.prototype.hide
 * @description 隐藏Toast
 * @param {Element} toast - Toast元素
 */
ToastComponent.prototype.hide = function(toast) {
    if (!toast || !toast.parentNode) {
        return;
    }

    // 隐藏动画
    toast.style.transform = 'translateX(100%)';
    toast.style.opacity = '0';

    // 移除元素
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
        
        // 从活动列表中移除
        const index = this.activeToasts.indexOf(toast);
        if (index > -1) {
            this.activeToasts.splice(index, 1);
        }
    }, this.config.animationDuration);
};

/**
 * @function ToastComponent.prototype.removeOldestToast
 * @description 移除最旧的Toast
 */
ToastComponent.prototype.removeOldestToast = function() {
    if (this.activeToasts.length > 0) {
        this.hide(this.activeToasts[0]);
    }
};

/**
 * @function ToastComponent.prototype.clear
 * @description 清除所有Toast
 */
ToastComponent.prototype.clear = function() {
    this.activeToasts.slice().forEach(toast => {
        this.hide(toast);
    });
};

/**
 * @function ToastComponent.prototype.escapeHtml
 * @description 转义HTML字符
 * @param {string} text - 原始文本
 * @returns {string} 转义后的文本
 */
ToastComponent.prototype.escapeHtml = function(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
};

// 便捷方法
ToastComponent.prototype.success = function(message, duration) {
    this.show(message, 'success', duration);
};

ToastComponent.prototype.error = function(message, duration) {
    this.show(message, 'error', duration);
};

ToastComponent.prototype.warning = function(message, duration) {
    this.show(message, 'warning', duration);
};

ToastComponent.prototype.info = function(message, duration) {
    this.show(message, 'info', duration);
};

// 注册到全局命名空间
SmartOffice.Components = SmartOffice.Components || {};
SmartOffice.Components.Toast = ToastComponent;

SmartOffice.log('info', 'SmartOffice Toast通知组件模块初始化完成');
