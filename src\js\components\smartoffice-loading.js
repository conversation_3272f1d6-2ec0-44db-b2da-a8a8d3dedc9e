/**
 * @file SmartOffice加载状态组件
 * @description iOS风格的加载指示器组件，支持全屏和局部加载状态
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function LoadingComponent
 * @description 加载状态组件构造函数
 * @constructor
 */
function LoadingComponent() {
    /**
     * @property {Object} config - 加载组件配置
     */
    this.config = {
        spinnerSize: 32,
        overlayOpacity: 0.8,
        animationDuration: 300,
        zIndex: 9999
    };

    /**
     * @property {Element} overlay - 全屏遮罩层
     */
    this.overlay = null;

    /**
     * @property {boolean} isVisible - 是否可见
     */
    this.isVisible = false;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    this.init();
    SmartOffice.log('info', 'LoadingComponent加载状态组件初始化完成');
}

/**
 * @function LoadingComponent.prototype.init
 * @description 初始化加载组件
 */
LoadingComponent.prototype.init = function() {
    this.createOverlay();
    this.bindEvents();
};

/**
 * @function LoadingComponent.prototype.createOverlay
 * @description 创建全屏遮罩层
 */
LoadingComponent.prototype.createOverlay = function() {
    // 检查是否已存在
    this.overlay = document.getElementById('loadingOverlay');

    if (!this.overlay) {
        this.overlay = document.createElement('div');
        this.overlay.id = 'loadingOverlay';
        this.overlay.className = 'loading-overlay';

        // 创建加载内容
        this.overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">加载中...</div>
            </div>
        `;

        // 设置样式
        this.setOverlayStyles();

        document.body.appendChild(this.overlay);
    }
};

/**
 * @function LoadingComponent.prototype.setOverlayStyles
 * @description 设置遮罩层样式
 */
LoadingComponent.prototype.setOverlayStyles = function() {
    // 遮罩层样式
    this.overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, ${this.config.overlayOpacity});
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        z-index: ${this.config.zIndex};
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all ${this.config.animationDuration}ms ease;
    `;

    // 加载内容样式
    const content = this.overlay.querySelector('.loading-content');
    content.style.cssText = `
        text-align: center;
        color: #FFFFFF;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    // 加载动画样式
    const spinner = this.overlay.querySelector('.loading-spinner');
    spinner.style.cssText = `
        width: ${this.config.spinnerSize}px;
        height: ${this.config.spinnerSize}px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid #FFFFFF;
        border-radius: 50%;
        margin: 0 auto 16px;
        animation: loadingSpinner 1s linear infinite;
    `;

    // 加载文本样式
    const text = this.overlay.querySelector('.loading-text');
    text.style.cssText = `
        font-size: 16px;
        font-weight: 500;
        opacity: 0.9;
    `;

    // 添加动画关键帧
    this.addSpinnerAnimation();
};

/**
 * @function LoadingComponent.prototype.addSpinnerAnimation
 * @description 添加旋转动画
 */
LoadingComponent.prototype.addSpinnerAnimation = function() {
    // 检查是否已添加动画
    if (document.getElementById('loadingSpinnerAnimation')) {
        return;
    }

    const style = document.createElement('style');
    style.id = 'loadingSpinnerAnimation';
    style.textContent = `
        @keyframes loadingSpinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
};

/**
 * @function LoadingComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
LoadingComponent.prototype.bindEvents = function() {
    const self = this;

    // 监听显示加载事件
    this.eventBus.on(SmartOffice.Events.UI_LOADING_SHOW, function(data) {
        const message = (data && data.message) || '加载中...';
        self.show(message);
    });

    // 监听隐藏加载事件
    this.eventBus.on(SmartOffice.Events.UI_LOADING_HIDE, function() {
        self.hide();
    });
};

/**
 * @function LoadingComponent.prototype.show
 * @description 显示加载状态
 * @param {string} message - 加载消息
 */
LoadingComponent.prototype.show = function(message) {
    if (this.isVisible) {
        // 如果已显示，只更新消息
        this.updateMessage(message);
        return;
    }

    // 更新消息
    if (message) {
        this.updateMessage(message);
    }

    // 显示遮罩层
    this.overlay.style.visibility = 'visible';
    this.overlay.style.opacity = '1';

    this.isVisible = true;

    // 防止页面滚动
    document.body.style.overflow = 'hidden';

    SmartOffice.log('info', '加载状态显示:', message || '加载中...');
};

/**
 * @function LoadingComponent.prototype.hide
 * @description 隐藏加载状态
 */
LoadingComponent.prototype.hide = function() {
    if (!this.isVisible) {
        return;
    }

    // 隐藏遮罩层
    this.overlay.style.opacity = '0';

    setTimeout(() => {
        this.overlay.style.visibility = 'hidden';
    }, this.config.animationDuration);

    this.isVisible = false;

    // 恢复页面滚动
    document.body.style.overflow = '';

    SmartOffice.log('info', '加载状态隐藏');
};

/**
 * @function LoadingComponent.prototype.updateMessage
 * @description 更新加载消息
 * @param {string} message - 新消息
 */
LoadingComponent.prototype.updateMessage = function(message) {
    const textElement = this.overlay.querySelector('.loading-text');
    if (textElement && message) {
        textElement.textContent = message;
    }
};

/**
 * @function LoadingComponent.prototype.createLocalLoader
 * @description 创建局部加载器
 * @param {Element} container - 容器元素
 * @param {string} message - 加载消息
 * @returns {Element} 加载器元素
 */
LoadingComponent.prototype.createLocalLoader = function(container, message) {
    const loader = document.createElement('div');
    loader.className = 'local-loading';

    loader.innerHTML = `
        <div class="local-loading-content">
            <div class="local-loading-spinner"></div>
            <div class="local-loading-text">${message || '加载中...'}</div>
        </div>
    `;

    // 设置局部加载器样式
    loader.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
    `;

    const content = loader.querySelector('.local-loading-content');
    content.style.cssText = `
        text-align: center;
        color: #333333;
    `;

    const spinner = loader.querySelector('.local-loading-spinner');
    spinner.style.cssText = `
        width: 24px;
        height: 24px;
        border: 2px solid rgba(0, 0, 0, 0.1);
        border-top: 2px solid #007AFF;
        border-radius: 50%;
        margin: 0 auto 8px;
        animation: loadingSpinner 1s linear infinite;
    `;

    const text = loader.querySelector('.local-loading-text');
    text.style.cssText = `
        font-size: 14px;
        color: #666666;
    `;

    // 确保容器有相对定位
    if (container.style.position !== 'absolute' && container.style.position !== 'fixed') {
        container.style.position = 'relative';
    }

    container.appendChild(loader);
    return loader;
};

/**
 * @function LoadingComponent.prototype.removeLocalLoader
 * @description 移除局部加载器
 * @param {Element} loader - 加载器元素
 */
LoadingComponent.prototype.removeLocalLoader = function(loader) {
    if (loader && loader.parentNode) {
        loader.parentNode.removeChild(loader);
    }
};

// 注册到全局命名空间
SmartOffice.Components = SmartOffice.Components || {};
SmartOffice.Components.Loading = LoadingComponent;

SmartOffice.log('info', 'SmartOffice加载组件模块初始化完成');
