/**
 * @file SmartOffice聚合模式管理器
 * @description 管理透视表字段的聚合函数配置，支持全局聚合模式设置
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function AggregationManager
 * @description 聚合模式管理器构造函数
 * @constructor
 */
function AggregationManager() {
    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;

    /**
     * @property {string} aggregationConfigKey - 聚合配置存储键名
     */
    this.aggregationConfigKey = 'global_aggregation_config';

    /**
     * @property {Array} supportedAggregations - 支持的聚合函数列表
     */
    this.supportedAggregations = [
        { name: 'sum', label: '求和', icon: '∑', description: '计算数值字段的总和' },
        { name: 'average', label: '平均值', icon: '⌀', description: '计算数值字段的平均值' },
        { name: 'count', label: '计数', icon: '#', description: '统计记录数量' },
        { name: 'max', label: '最大值', icon: '↑', description: '找出数值字段的最大值' },
        { name: 'min', label: '最小值', icon: '↓', description: '找出数值字段的最小值' },
        { name: 'median', label: '中位数', icon: '⌐', description: '计算数值字段的中位数' },
        { name: 'distinct', label: '去重计数', icon: '∪', description: '统计不重复值的数量' }
    ];

    /**
     * @property {Object} currentAggregationConfig - 当前聚合配置
     */
    this.currentAggregationConfig = {};

    /**
     * @property {boolean} multiAggregationMode - 是否启用多聚合模式
     */
    this.multiAggregationMode = true;

    SmartOffice.log('info', 'AggregationManager聚合模式管理器初始化完成');
}

/**
 * @function AggregationManager.prototype.init
 * @description 初始化聚合模式管理器
 */
AggregationManager.prototype.init = function() {
    try {
        // 加载保存的聚合配置
        this.loadAggregationConfig();

        SmartOffice.log('info', '聚合模式管理器初始化成功');
    } catch (error) {
        SmartOffice.log('error', '聚合模式管理器初始化失败:', error);
    }
};

/**
 * @function AggregationManager.prototype.loadAggregationConfig
 * @description 加载聚合配置
 */
AggregationManager.prototype.loadAggregationConfig = function() {
    try {
        const savedConfig = this.storage.get(this.aggregationConfigKey);
        if (savedConfig && typeof savedConfig === 'object') {
            this.currentAggregationConfig = savedConfig;
            SmartOffice.log('info', '聚合配置加载成功');
        } else {
            this.currentAggregationConfig = {};
            SmartOffice.log('info', '使用默认聚合配置');
        }
    } catch (error) {
        SmartOffice.log('error', '加载聚合配置失败:', error);
        this.currentAggregationConfig = {};
    }
};

/**
 * @function AggregationManager.prototype.saveAggregationConfig
 * @description 保存聚合配置
 */
AggregationManager.prototype.saveAggregationConfig = function() {
    try {
        this.storage.set(this.aggregationConfigKey, this.currentAggregationConfig);
        SmartOffice.log('info', '聚合配置保存成功');
        
        // 触发配置更新事件
        this.eventBus.emit('aggregation:configUpdated', this.currentAggregationConfig);
    } catch (error) {
        SmartOffice.log('error', '保存聚合配置失败:', error);
    }
};

/**
 * @function AggregationManager.prototype.setFieldAggregation
 * @description 设置字段的聚合函数
 * @param {string} fieldName - 字段名称
 * @param {string|Array} aggregations - 聚合函数名称或数组
 */
AggregationManager.prototype.setFieldAggregation = function(fieldName, aggregations) {
    if (!fieldName) {
        SmartOffice.log('error', '字段名称不能为空');
        return;
    }

    // 确保聚合函数是数组格式
    if (typeof aggregations === 'string') {
        aggregations = [aggregations];
    }

    if (!Array.isArray(aggregations)) {
        SmartOffice.log('error', '聚合函数必须是字符串或数组');
        return;
    }

    // 验证聚合函数是否支持
    const validAggregations = [];
    for (let i = 0; i < aggregations.length; i++) {
        const agg = aggregations[i];
        if (this.isValidAggregation(agg)) {
            validAggregations.push(agg);
        } else {
            SmartOffice.log('warn', '不支持的聚合函数:', agg);
        }
    }

    if (validAggregations.length === 0) {
        SmartOffice.log('error', '没有有效的聚合函数');
        return;
    }

    // 设置字段聚合配置
    this.currentAggregationConfig[fieldName] = validAggregations;

    // 保存配置
    this.saveAggregationConfig();

    SmartOffice.log('info', '字段聚合函数设置成功:', fieldName, validAggregations);
};

/**
 * @function AggregationManager.prototype.getFieldAggregation
 * @description 获取字段的聚合函数配置
 * @param {string} fieldName - 字段名称
 * @returns {Array} 聚合函数数组
 */
AggregationManager.prototype.getFieldAggregation = function(fieldName) {
    if (!fieldName) {
        return [];
    }

    return this.currentAggregationConfig[fieldName] || [];
};

/**
 * @function AggregationManager.prototype.removeFieldAggregation
 * @description 移除字段的聚合函数配置
 * @param {string} fieldName - 字段名称
 */
AggregationManager.prototype.removeFieldAggregation = function(fieldName) {
    if (!fieldName) {
        return;
    }

    if (this.currentAggregationConfig[fieldName]) {
        delete this.currentAggregationConfig[fieldName];
        this.saveAggregationConfig();
        SmartOffice.log('info', '字段聚合函数配置已移除:', fieldName);
    }
};

/**
 * @function AggregationManager.prototype.getAllFieldAggregations
 * @description 获取所有字段的聚合函数配置
 * @returns {Object} 聚合配置对象
 */
AggregationManager.prototype.getAllFieldAggregations = function() {
    return Object.assign({}, this.currentAggregationConfig);
};

/**
 * @function AggregationManager.prototype.clearAllAggregations
 * @description 清空所有聚合函数配置
 */
AggregationManager.prototype.clearAllAggregations = function() {
    this.currentAggregationConfig = {};
    this.saveAggregationConfig();
    SmartOffice.log('info', '所有聚合函数配置已清空');
};

/**
 * @function AggregationManager.prototype.isValidAggregation
 * @description 检查聚合函数是否有效
 * @param {string} aggregation - 聚合函数名称
 * @returns {boolean} 是否有效
 */
AggregationManager.prototype.isValidAggregation = function(aggregation) {
    return this.supportedAggregations.some(function(agg) {
        return agg.name === aggregation;
    });
};

/**
 * @function AggregationManager.prototype.getSupportedAggregations
 * @description 获取支持的聚合函数列表
 * @returns {Array} 聚合函数列表
 */
AggregationManager.prototype.getSupportedAggregations = function() {
    return this.supportedAggregations.slice(); // 返回副本
};

/**
 * @function AggregationManager.prototype.getAggregationInfo
 * @description 获取聚合函数信息
 * @param {string} aggregation - 聚合函数名称
 * @returns {Object|null} 聚合函数信息
 */
AggregationManager.prototype.getAggregationInfo = function(aggregation) {
    return this.supportedAggregations.find(function(agg) {
        return agg.name === aggregation;
    }) || null;
};

/**
 * @function AggregationManager.prototype.generateValueFieldLabels
 * @description 为透视表值字段生成带聚合函数的标签
 * @param {Array} valueFields - 值字段数组
 * @returns {Array} 带标签的值字段数组
 */
AggregationManager.prototype.generateValueFieldLabels = function(valueFields) {
    const labeledFields = [];

    for (let i = 0; i < valueFields.length; i++) {
        const field = valueFields[i];
        const aggregations = this.getFieldAggregation(field.name);

        if (aggregations.length === 0) {
            // 没有配置聚合函数，使用默认
            labeledFields.push({
                name: field.name + '_求和',
                originalName: field.name,
                label: field.label + '_求和',
                aggregation: 'sum',
                type: field.type
            });
        } else {
            // 为每个聚合函数创建一个字段
            for (let j = 0; j < aggregations.length; j++) {
                const agg = aggregations[j];
                const aggInfo = this.getAggregationInfo(agg);
                const aggLabel = aggInfo ? aggInfo.label : agg;

                labeledFields.push({
                    name: field.name + '_' + aggLabel,
                    originalName: field.name,
                    label: field.label + '_' + aggLabel,
                    aggregation: agg,
                    type: field.type
                });
            }
        }
    }

    return labeledFields;
};

// 创建全局实例
SmartOffice.Data.AggregationManager = new AggregationManager();

SmartOffice.log('info', 'SmartOffice聚合模式管理器模块初始化完成');
