/**
 * @file SmartOffice字段选择器组件
 * @description iOS风格的字段选择器，支持基于上传数据动态生成字段选择界面
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function FieldSelectorComponent
 * @description 字段选择器组件构造函数
 * @constructor
 * @param {Object} options - 配置选项
 * @param {string} options.containerId - 容器元素ID
 * @param {Array} options.fields - 可选字段数组
 * @param {string} options.fieldType - 字段类型 (rowFields, columnFields, valueFields, filterFields)
 * @param {boolean} options.multiple - 是否支持多选（默认true）
 * @param {string} options.title - 选择器标题
 * @param {Function} options.onSelect - 选择回调函数
 * @param {Function} options.onCancel - 取消回调函数
 */
function FieldSelectorComponent(options) {
    // 默认配置
    this.options = SmartOffice.Utils.Helpers.extend({
        containerId: 'fieldSelectorContainer',
        fields: [],
        fieldType: 'rowFields',
        multiple: true,
        title: '选择字段',
        maxSelections: 10,
        showFieldTypes: true,
        onSelect: null,
        onCancel: null
    }, options || {});
    
    // 组件状态
    this.isVisible = false;
    this.selectedFields = [];
    this.availableFields = [];
    
    // DOM元素引用
    this.containerElement = null;
    this.modalElement = null;
    this.backdropElement = null;
    this.headerElement = null;
    this.listElement = null;
    this.footerElement = null;
    
    // 依赖注入
    this.dom = SmartOffice.Utils.DOM;
    this.helpers = SmartOffice.Utils.Helpers;
    this.eventBus = SmartOffice.Core.EventBus;
    this.dataValidator = new SmartOffice.Data.DataValidator();

    /**
     * @property {Object} fieldMappingManager - 字段映射管理器引用
     */
    this.fieldMappingManager = SmartOffice.Data.FieldMappingManager;
    
    SmartOffice.log('info', 'FieldSelectorComponent初始化完成');
}

/**
 * @function FieldSelectorComponent.prototype.init
 * @description 初始化字段选择器组件
 * @returns {boolean} 初始化是否成功
 */
FieldSelectorComponent.prototype.init = function() {
    try {
        // 获取容器元素
        this.containerElement = document.getElementById(this.options.containerId);
        if (!this.containerElement) {
            throw new Error('找不到字段选择器容器元素: ' + this.options.containerId);
        }
        
        // 处理字段数据
        this.processFields();
        
        // 渲染组件界面
        this.render();
        
        // 绑定事件监听器
        this.bindEvents();

        // 监听字段映射变更事件
        this.bindMappingEvents();

        SmartOffice.log('info', '字段选择器组件初始化成功');
        return true;
    } catch (error) {
        SmartOffice.log('error', '字段选择器组件初始化失败:', error);
        return false;
    }
};

/**
 * @function FieldSelectorComponent.prototype.processFields
 * @description 处理字段数据，添加类型信息，集成字段映射数据
 */
FieldSelectorComponent.prototype.processFields = function() {
    this.availableFields = [];

    // 首先尝试获取映射后的字段
    const mappedFields = this.getMappedFields();

    // 如果有映射字段，优先使用映射字段
    if (mappedFields && mappedFields.length > 0) {
        SmartOffice.log('info', '使用映射后的字段数据:', mappedFields.length + '个字段');
        this.availableFields = mappedFields;
    } else {
        // 否则使用原始字段数据
        for (let i = 0; i < this.options.fields.length; i++) {
            const field = this.options.fields[i];

            // 如果字段是字符串，转换为对象
            if (typeof field === 'string') {
                this.availableFields.push({
                    name: field,
                    label: field,
                    type: this.detectFieldType(field),
                    description: '',
                    mapped: false,
                    originalName: field
                });
            } else {
                this.availableFields.push({
                    name: field.name || field.label,
                    label: field.label || field.name,
                    type: field.type || this.detectFieldType(field.name),
                    description: field.description || '',
                    mapped: field.mapped || false,
                    originalName: field.originalName || field.name || field.label
                });
            }
        }
    }

    SmartOffice.log('info', '已处理字段数据:', this.availableFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.getMappedFields
 * @description 获取映射后的字段列表
 * @returns {Array} 映射后的字段列表
 */
FieldSelectorComponent.prototype.getMappedFields = function() {
    try {
        if (!this.fieldMappingManager) {
            return [];
        }

        // 获取已映射的字段
        const mappedFields = this.fieldMappingManager.getMappedFields();

        if (!mappedFields || mappedFields.length === 0) {
            return [];
        }

        // 转换为字段选择器需要的格式
        return mappedFields.map(function(field) {
            return {
                name: field.name,
                label: field.label,
                type: field.type,
                description: '映射字段: ' + field.originalName,
                mapped: true,
                originalName: field.originalName,
                mapping: field.mapping
            };
        });

    } catch (error) {
        SmartOffice.log('error', '获取映射字段失败:', error);
        return [];
    }
};

/**
 * @function FieldSelectorComponent.prototype.setDataSource
 * @description 设置数据源用于字段类型检测
 * @param {Object} dataSource - 数据源对象（包含data和headers）
 */
FieldSelectorComponent.prototype.setDataSource = function(dataSource) {
    this.dataSource = dataSource;

    // 重新处理字段类型
    if (this.options.fields && this.options.fields.length > 0) {
        this.processFields();

        // 如果选择器已经渲染，重新渲染字段列表
        if (this.listElement) {
            this.listElement.innerHTML = this.renderFieldList();
            // 重新绑定字段列表的事件监听器
            this.bindFieldListEvents();
        }
    }

    SmartOffice.log('info', '数据源已设置，字段类型已更新');
};

/**
 * @function FieldSelectorComponent.prototype.detectFieldType
 * @description 检测字段类型 - 使用数据验证器进行准确的类型检测
 * @param {string} fieldName - 字段名称
 * @returns {string} 字段类型
 */
FieldSelectorComponent.prototype.detectFieldType = function(fieldName) {
    if (!fieldName || typeof fieldName !== 'string') {
        return 'string';
    }

    // 如果有可用的数据样本，使用数据验证器进行准确检测
    if (this.dataSource && this.dataSource.data && this.dataSource.data.length > 0) {
        if (!this.dataValidator) {
            this.dataValidator = new SmartOffice.Data.DataValidator();
        }

        // 分析前几行数据来确定字段类型
        const sampleSize = Math.min(this.dataSource.data.length, 10);
        const typeCounts = { string: 0, number: 0, date: 0, time: 0, empty: 0 };

        for (let i = 0; i < sampleSize; i++) {
            const value = this.dataSource.data[i][fieldName];
            const type = this.dataValidator.detectDataType(value);
            typeCounts[type] = (typeCounts[type] || 0) + 1;
        }

        // 找出主要类型
        let dominantType = 'string';
        let maxCount = 0;
        for (const type in typeCounts) {
            if (type !== 'empty' && typeCounts[type] > maxCount) {
                maxCount = typeCounts[type];
                dominantType = type;
            }
        }

        return dominantType;
    }

    // 如果没有数据样本，使用基于字段名称的简单检测作为后备
    const name = fieldName.toLowerCase();

    // 数值类型字段
    if (name.includes('amount') || name.includes('price') || name.includes('cost') ||
        name.includes('total') || name.includes('sum') || name.includes('count') ||
        name.includes('quantity') || name.includes('qty') || name.includes('number') ||
        name.includes('销售额') || name.includes('金额') || name.includes('数量')) {
        return 'number';
    }

    // 时间类型字段
    if (name.includes('time') || name.includes('时间')) {
        return 'time';
    }

    // 日期类型字段
    if (name.includes('date') || name.includes('created') || name.includes('updated') ||
        name.includes('modified') || name.includes('日期')) {
        return 'date';
    }

    // 默认为文本类型
    return 'string';
};

/**
 * @function FieldSelectorComponent.prototype.render
 * @description 渲染字段选择器界面
 */
FieldSelectorComponent.prototype.render = function() {
    const html = `
        <!-- 背景遮罩 -->
        <div class="so-field-selector-backdrop" id="fieldSelectorBackdrop" style="display: none;"></div>
        
        <!-- 选择器模态框 -->
        <div class="so-field-selector-modal" id="fieldSelectorModal" style="display: none;">
            <!-- 头部 -->
            <div class="so-field-selector-header" id="fieldSelectorHeader">
                <button type="button" class="so-header-button cancel" id="cancelButton">取消</button>
                <h2 class="so-header-title">${this.options.title}</h2>
                <button type="button" class="so-header-button confirm" id="confirmButton">确定</button>
            </div>
            
            <!-- 字段列表 -->
            <div class="so-field-selector-content">
                <div class="so-field-list" id="fieldList">
                    ${this.renderFieldList()}
                </div>
            </div>
            
            <!-- 底部信息 -->
            <div class="so-field-selector-footer" id="fieldSelectorFooter">
                <div class="so-selection-info">
                    <span id="selectionCount">已选择 0 个字段</span>
                    ${this.options.multiple ? `<span class="so-max-info">最多可选择 ${this.options.maxSelections} 个</span>` : ''}
                </div>
            </div>
        </div>
    `;
    
    this.containerElement.innerHTML = html;
    
    // 获取DOM元素引用
    this.modalElement = document.getElementById('fieldSelectorModal');
    this.backdropElement = document.getElementById('fieldSelectorBackdrop');
    this.headerElement = document.getElementById('fieldSelectorHeader');
    this.listElement = document.getElementById('fieldList');
    this.footerElement = document.getElementById('fieldSelectorFooter');
};

/**
 * @function FieldSelectorComponent.prototype.renderFieldList
 * @description 渲染字段列表
 * @returns {string} 字段列表HTML
 */
FieldSelectorComponent.prototype.renderFieldList = function() {
    if (this.availableFields.length === 0) {
        return '<div class="so-field-empty">暂无可选字段</div>';
    }
    
    let html = '';
    for (let i = 0; i < this.availableFields.length; i++) {
        const field = this.availableFields[i];
        const isSelected = this.isFieldSelected(field);
        
        const mappedClass = field.mapped ? 'mapped-field' : '';

        html += `
            <div class="so-field-item ${isSelected ? 'selected' : ''} ${mappedClass}"
                 data-field-name="${this.helpers.escapeHtml(field.name)}"
                 data-field-index="${i}">
                <div class="so-field-content">
                    <div class="so-field-main">
                        <span class="so-field-name">${this.helpers.escapeHtml(field.label)}</span>
                        ${field.mapped ? this.renderMappedIndicator() : ''}
                        ${this.options.showFieldTypes ? this.renderFieldTypeTag(field.type) : ''}
                        ${field.type === 'time' ? this.renderTimeRangeButton(field) : ''}
                        ${this.currentFieldType === 'valueFields' && field.type === 'number' ? this.renderAggregationButton(field) : ''}
                    </div>
                    ${field.description ? `<div class="so-field-description">${this.helpers.escapeHtml(field.description)}</div>` : ''}
                    ${field.type === 'time' && field.timeRanges ? this.renderTimeRangeDisplay(field.timeRanges) : ''}
                    ${this.currentFieldType === 'valueFields' && field.type === 'number' && field.aggregations ? this.renderAggregationDisplay(field.aggregations) : ''}
                </div>
                <div class="so-field-selector">
                    ${this.options.multiple ? this.renderCheckbox(isSelected) : this.renderRadio(isSelected)}
                </div>
            </div>
        `;
    }
    
    return html;
};

/**
 * @function FieldSelectorComponent.prototype.renderMappedIndicator
 * @description 渲染映射字段指示器
 * @returns {string} 映射指示器HTML
 */
FieldSelectorComponent.prototype.renderMappedIndicator = function() {
    return `<span class="so-field-mapped-indicator" title="已映射字段">
        <svg viewBox="0 0 24 24" class="mapped-icon">
            <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8L6,7L4,9M4,19H8L6,17L4,19M4,14H8L6,12L4,14Z"/>
        </svg>
    </span>`;
};

/**
 * @function FieldSelectorComponent.prototype.renderFieldTypeTag
 * @description 渲染字段类型标签
 * @param {string} type - 字段类型
 * @returns {string} 类型标签HTML
 */
FieldSelectorComponent.prototype.renderFieldTypeTag = function(type) {
    const typeLabels = {
        'number': '数值',
        'date': '日期',
        'time': '时间',
        'string': '文本',
        'text': '文本'  // 兼容旧的text类型
    };

    const label = typeLabels[type] || '文本';
    return `<span class="so-field-type so-field-type-${type}">${label}</span>`;
};

/**
 * @function FieldSelectorComponent.prototype.renderTimeRangeButton
 * @description 渲染时间区间设置按钮
 * @param {Object} field - 字段对象
 * @returns {string} 时间区间按钮HTML
 */
FieldSelectorComponent.prototype.renderTimeRangeButton = function(field) {
    return `
        <button type="button"
                class="so-time-range-btn"
                data-field-name="${this.helpers.escapeHtml(field.name)}"
                title="设置时间区间">
            <svg viewBox="0 0 24 24" class="time-range-icon">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
            </svg>
        </button>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.renderTimeRangeDisplay
 * @description 渲染时间区间显示
 * @param {Array} timeRanges - 时间区间数组
 * @returns {string} 时间区间显示HTML
 */
FieldSelectorComponent.prototype.renderTimeRangeDisplay = function(timeRanges) {
    if (!timeRanges || timeRanges.length === 0) {
        return '';
    }

    let html = '<div class="so-time-ranges">';
    for (let i = 0; i < timeRanges.length; i++) {
        const range = timeRanges[i];
        html += `
            <span class="so-time-range-tag">
                ${range.name || `${range.start}-${range.end}`}
            </span>
        `;
    }
    html += '</div>';

    return html;
};

/**
 * @function FieldSelectorComponent.prototype.renderAggregationButton
 * @description 渲染聚合方式设置按钮
 * @param {Object} field - 字段对象
 * @returns {string} 聚合按钮HTML
 */
FieldSelectorComponent.prototype.renderAggregationButton = function(field) {
    return `
        <button type="button"
                class="so-aggregation-btn"
                data-field-name="${this.helpers.escapeHtml(field.name)}"
                title="设置聚合方式">
            <svg viewBox="0 0 24 24" class="aggregation-icon">
                <path d="M3,3H21V5H3V3M3,7H15V9H3V7M3,11H21V13H3V11M3,15H15V17H3V15M3,19H21V21H3V19Z"/>
            </svg>
        </button>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.renderAggregationDisplay
 * @description 渲染聚合方式显示
 * @param {Array} aggregations - 聚合方式数组
 * @returns {string} 聚合方式显示HTML
 */
FieldSelectorComponent.prototype.renderAggregationDisplay = function(aggregations) {
    if (!aggregations || aggregations.length === 0) {
        return '';
    }

    let html = '<div class="so-aggregations">';
    for (let i = 0; i < aggregations.length; i++) {
        const agg = aggregations[i];
        const label = this.getAggregationLabel(agg);
        html += `
            <span class="so-aggregation-tag">
                ${label}
            </span>
        `;
    }
    html += '</div>';

    return html;
};

/**
 * @function FieldSelectorComponent.prototype.getAggregationLabel
 * @description 获取聚合类型的中文标签
 * @param {string} aggregationType - 聚合类型
 * @returns {string} 中文标签
 */
FieldSelectorComponent.prototype.getAggregationLabel = function(aggregationType) {
    const labels = {
        'sum': '求和',
        'count': '计数',
        'average': '平均值',
        'min': '最小值',
        'max': '最大值',
        'countDistinct': '去重计数'
    };

    return labels[aggregationType] || aggregationType;
};

/**
 * @function FieldSelectorComponent.prototype.renderCheckbox
 * @description 渲染复选框
 * @param {boolean} checked - 是否选中
 * @returns {string} 复选框HTML
 */
FieldSelectorComponent.prototype.renderCheckbox = function(checked) {
    return `
        <span class="so-field-checkbox ${checked ? 'checked' : ''}">
            <svg viewBox="0 0 24 24" class="checkbox-icon">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
        </span>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.renderRadio
 * @description 渲染单选框
 * @param {boolean} checked - 是否选中
 * @returns {string} 单选框HTML
 */
FieldSelectorComponent.prototype.renderRadio = function(checked) {
    return `
        <span class="so-field-radio ${checked ? 'checked' : ''}">
            <span class="so-radio-inner"></span>
        </span>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
FieldSelectorComponent.prototype.bindEvents = function() {
    const self = this;

    // 延迟绑定按钮事件，确保DOM元素已创建
    setTimeout(() => {
        self.bindHeaderButtonEvents();
    }, 10);

    // 背景遮罩点击事件
    if (this.backdropElement) {
        this.backdropElement.addEventListener('click', function() {
            self.cancel();
        });
    }

    // 绑定字段列表事件
    this.bindFieldListEvents();

    // 键盘事件
    document.addEventListener('keydown', function(event) {
        if (self.isVisible) {
            if (event.key === 'Escape') {
                self.cancel();
            } else if (event.key === 'Enter') {
                self.confirm();
            }
        }
    });
};

/**
 * @function FieldSelectorComponent.prototype.bindMappingEvents
 * @description 绑定字段映射变更事件监听器
 */
FieldSelectorComponent.prototype.bindMappingEvents = function() {
    const self = this;

    // 监听字段映射保存事件
    this.eventBus.on('fieldMapping:currentMappingSaved', function(data) {
        self.onMappingChanged(data);
    });

    // 监听字段映射清除事件
    this.eventBus.on('fieldMapping:currentMappingCleared', function() {
        self.onMappingCleared();
    });

    // 监听映射添加事件
    this.eventBus.on('fieldMapping:mappingAdded', function(mapping) {
        self.onMappingAdded(mapping);
    });

    // 监听映射移除事件
    this.eventBus.on('fieldMapping:mappingRemoved', function(mapping) {
        self.onMappingRemoved(mapping);
    });

    // 监听模板应用事件
    this.eventBus.on('mappingTemplate:applied', function(data) {
        self.onTemplateApplied(data);
    });
};

/**
 * @function FieldSelectorComponent.prototype.onMappingChanged
 * @description 处理字段映射变更事件
 * @param {Object} data - 映射数据
 */
FieldSelectorComponent.prototype.onMappingChanged = function(data) {
    try {
        SmartOffice.log('info', '字段映射已更新，重新加载字段列表');

        // 重新处理字段数据
        this.processFields();

        // 如果选择器已经渲染，重新渲染字段列表
        if (this.listElement) {
            this.listElement.innerHTML = this.renderFieldList();
            this.bindFieldListEvents();
        }

        // 触发字段更新事件
        this.eventBus.emit('fieldSelector:fieldsUpdated', {
            fields: this.availableFields,
            mappedCount: data.mappings ? data.mappings.length : 0
        });

    } catch (error) {
        SmartOffice.log('error', '处理字段映射变更失败:', error);
    }
};

/**
 * @function FieldSelectorComponent.prototype.onMappingCleared
 * @description 处理字段映射清除事件
 */
FieldSelectorComponent.prototype.onMappingCleared = function() {
    try {
        SmartOffice.log('info', '字段映射已清除，恢复原始字段列表');

        // 重新处理字段数据
        this.processFields();

        // 如果选择器已经渲染，重新渲染字段列表
        if (this.listElement) {
            this.listElement.innerHTML = this.renderFieldList();
            this.bindFieldListEvents();
        }

        // 触发字段更新事件
        this.eventBus.emit('fieldSelector:fieldsUpdated', {
            fields: this.availableFields,
            mappedCount: 0
        });

    } catch (error) {
        SmartOffice.log('error', '处理字段映射清除失败:', error);
    }
};

/**
 * @function FieldSelectorComponent.prototype.onMappingAdded
 * @description 处理单个映射添加事件
 * @param {Object} mapping - 映射对象
 */
FieldSelectorComponent.prototype.onMappingAdded = function(mapping) {
    try {
        // 实时更新字段列表
        this.onMappingChanged({ mappings: [mapping] });
    } catch (error) {
        SmartOffice.log('error', '处理映射添加失败:', error);
    }
};

/**
 * @function FieldSelectorComponent.prototype.onMappingRemoved
 * @description 处理单个映射移除事件
 * @param {Object} mapping - 映射对象
 */
FieldSelectorComponent.prototype.onMappingRemoved = function(mapping) {
    try {
        // 实时更新字段列表
        this.onMappingChanged({ mappings: [] });
    } catch (error) {
        SmartOffice.log('error', '处理映射移除失败:', error);
    }
};

/**
 * @function FieldSelectorComponent.prototype.onTemplateApplied
 * @description 处理模板应用事件
 * @param {Object} data - 模板应用数据
 */
FieldSelectorComponent.prototype.onTemplateApplied = function(data) {
    try {
        SmartOffice.log('info', '映射模板已应用:', data.template.name);

        // 重新处理字段数据
        this.processFields();

        // 如果选择器已经渲染，重新渲染字段列表
        if (this.listElement) {
            this.listElement.innerHTML = this.renderFieldList();
            this.bindFieldListEvents();
        }

        // 触发字段更新事件
        this.eventBus.emit('fieldSelector:fieldsUpdated', {
            fields: this.availableFields,
            mappedCount: data.mappings ? data.mappings.length : 0,
            template: data.template
        });

    } catch (error) {
        SmartOffice.log('error', '处理模板应用失败:', error);
    }
};

/**
 * @function FieldSelectorComponent.prototype.bindHeaderButtonEvents
 * @description 绑定头部按钮事件监听器
 */
FieldSelectorComponent.prototype.bindHeaderButtonEvents = function() {
    const self = this;

    // 移除之前的事件监听器（如果存在）
    const cancelButton = this.containerElement.querySelector('#cancelButton');
    const confirmButton = this.containerElement.querySelector('#confirmButton');

    if (cancelButton && this.cancelButtonHandler) {
        cancelButton.removeEventListener('click', this.cancelButtonHandler);
    }
    if (confirmButton && this.confirmButtonHandler) {
        confirmButton.removeEventListener('click', this.confirmButtonHandler);
    }

    // 创建新的事件处理函数
    this.cancelButtonHandler = function(event) {
        console.log('🔘 取消按钮被点击');
        event.preventDefault();
        event.stopPropagation();
        self.cancel();
    };

    this.confirmButtonHandler = function(event) {
        console.log('🔘 确定按钮被点击');
        event.preventDefault();
        event.stopPropagation();
        self.confirm();
    };

    // 绑定取消按钮事件
    if (cancelButton) {
        console.log('✅ 找到取消按钮，绑定事件');
        cancelButton.addEventListener('click', this.cancelButtonHandler);
        this.dom.addTouchFeedback(cancelButton, 'light');
    } else {
        console.error('❌ 未找到取消按钮元素');
    }

    // 绑定确定按钮事件
    if (confirmButton) {
        console.log('✅ 找到确定按钮，绑定事件');
        confirmButton.addEventListener('click', this.confirmButtonHandler);
        this.dom.addTouchFeedback(confirmButton, 'light');
    } else {
        console.error('❌ 未找到确定按钮元素');
    }
};

/**
 * @function FieldSelectorComponent.prototype.bindFieldListEvents
 * @description 绑定字段列表的事件监听器
 */
FieldSelectorComponent.prototype.bindFieldListEvents = function() {
    const self = this;

    // 字段项点击事件
    if (this.listElement) {
        // 移除之前的事件监听器（如果存在）
        this.listElement.removeEventListener('click', this.fieldListClickHandler);
        this.listElement.removeEventListener('touchstart', this.fieldListTouchHandler);

        // 创建事件处理函数
        this.fieldListClickHandler = function(event) {
            console.log('🔍 字段列表点击事件:', event.target);

            // 检查是否点击了时间区间按钮
            const timeRangeBtn = event.target.closest('.so-time-range-btn');
            if (timeRangeBtn) {
                event.preventDefault();
                event.stopPropagation();
                console.log('⏰ 点击时间区间按钮');

                const fieldName = timeRangeBtn.dataset.fieldName;
                const field = self.availableFields.find(f => f.name === fieldName);
                if (field) {
                    self.showTimeRangeModal(field);
                }
                return;
            }

            // 检查是否点击了聚合方式按钮
            const aggregationBtn = event.target.closest('.so-aggregation-btn');
            if (aggregationBtn) {
                event.preventDefault();
                event.stopPropagation();
                console.log('📊 点击聚合方式按钮');

                const fieldName = aggregationBtn.dataset.fieldName;
                const field = self.availableFields.find(f => f.name === fieldName);
                if (field) {
                    self.showAggregationModal(field);
                }
                return;
            }

            const fieldItem = event.target.closest('.so-field-item');
            if (fieldItem) {
                event.preventDefault();
                event.stopPropagation();
                console.log('📋 点击字段项:', fieldItem.dataset.fieldName);

                const fieldName = fieldItem.dataset.fieldName;
                const fieldIndex = parseInt(fieldItem.dataset.fieldIndex);
                if (self.availableFields[fieldIndex]) {
                    self.toggleField(self.availableFields[fieldIndex]);
                } else {
                    console.error('❌ 字段索引无效:', fieldIndex);
                }
            } else {
                console.log('⚠️ 未找到字段项元素');
            }
        };

        this.fieldListTouchHandler = function(event) {
            console.log('👆 字段列表触摸开始');
        };

        // 绑定新的事件监听器
        this.listElement.addEventListener('click', this.fieldListClickHandler);
        this.listElement.addEventListener('touchstart', this.fieldListTouchHandler);
    }
};

/**
 * @function FieldSelectorComponent.prototype.show
 * @description 显示字段选择器
 * @param {Array} preSelectedFields - 预选字段
 * @param {string} fieldType - 字段类型（用于确定是否显示聚合选项）
 */
FieldSelectorComponent.prototype.show = function(preSelectedFields, fieldType) {
    if (this.isVisible) return;

    // 保存字段类型，用于判断是否显示聚合选项
    this.currentFieldType = fieldType;

    // 设置预选字段
    this.selectedFields = preSelectedFields ? [...preSelectedFields] : [];

    // 显示模态框
    this.isVisible = true;
    this.dom.addClass(this.containerElement, 'active');
    this.dom.setStyle(this.backdropElement, { display: 'block' });
    this.dom.setStyle(this.modalElement, { display: 'block' });

    // 添加显示动画类
    setTimeout(() => {
        this.dom.addClass(this.backdropElement, 'so-backdrop-visible');
        this.dom.addClass(this.modalElement, 'so-modal-visible');
    }, 10);

    // 更新界面状态
    this.updateFieldStates();
    this.updateSelectionInfo();

    // 重新绑定头部按钮事件（确保按钮可点击）
    setTimeout(() => {
        this.bindHeaderButtonEvents();
    }, 50);

    // 阻止背景滚动
    this.dom.addClass(document.body, 'so-modal-open');

    SmartOffice.log('info', '字段选择器已显示，字段类型:', fieldType);
};

/**
 * @function FieldSelectorComponent.prototype.hide
 * @description 隐藏字段选择器
 */
FieldSelectorComponent.prototype.hide = function() {
    if (!this.isVisible) return;

    this.isVisible = false;

    // 移除显示动画类
    this.dom.removeClass(this.backdropElement, 'so-backdrop-visible');
    this.dom.removeClass(this.modalElement, 'so-modal-visible');

    // 延迟隐藏元素
    setTimeout(() => {
        this.dom.setStyle(this.backdropElement, { display: 'none' });
        this.dom.setStyle(this.modalElement, { display: 'none' });
        this.dom.removeClass(this.containerElement, 'active');
    }, 300);

    // 恢复背景滚动
    this.dom.removeClass(document.body, 'so-modal-open');

    SmartOffice.log('info', '字段选择器已隐藏');
};

/**
 * @function FieldSelectorComponent.prototype.toggleField
 * @description 切换字段选择状态
 * @param {Object} field - 字段对象
 */
FieldSelectorComponent.prototype.toggleField = function(field) {
    if (!field) return;

    const index = this.selectedFields.findIndex(selected => selected.name === field.name);

    if (index > -1) {
        // 取消选择
        this.selectedFields.splice(index, 1);
    } else {
        // 添加选择
        if (this.options.multiple) {
            // 多选模式：检查数量限制
            if (this.selectedFields.length >= this.options.maxSelections) {
                this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW,
                    `最多只能选择${this.options.maxSelections}个字段`, 'warning');
                return;
            }
            this.selectedFields.push(field);
        } else {
            // 单选模式：替换选择
            this.selectedFields = [field];
        }
    }

    // 更新界面状态
    this.updateFieldStates();
    this.updateSelectionInfo();

    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    SmartOffice.log('info', '字段选择状态已切换:', field.name);
};

/**
 * @function FieldSelectorComponent.prototype.updateFieldStates
 * @description 更新字段状态显示
 */
FieldSelectorComponent.prototype.updateFieldStates = function() {
    const fieldItems = this.listElement.querySelectorAll('.so-field-item');

    for (let i = 0; i < fieldItems.length; i++) {
        const item = fieldItems[i];
        const fieldName = item.dataset.fieldName;
        const isSelected = this.selectedFields.some(field => field.name === fieldName);

        if (isSelected) {
            this.dom.addClass(item, 'selected');
        } else {
            this.dom.removeClass(item, 'selected');
        }

        // 更新选择器状态
        const selector = item.querySelector('.so-field-checkbox, .so-field-radio');
        if (selector) {
            if (isSelected) {
                this.dom.addClass(selector, 'checked');
            } else {
                this.dom.removeClass(selector, 'checked');
            }
        }
    }
};

/**
 * @function FieldSelectorComponent.prototype.updateSelectionInfo
 * @description 更新选择信息显示
 */
FieldSelectorComponent.prototype.updateSelectionInfo = function() {
    const selectionCount = document.getElementById('selectionCount');
    if (selectionCount) {
        const count = this.selectedFields.length;
        if (this.options.multiple) {
            selectionCount.textContent = `已选择 ${count} 个字段`;
        } else {
            selectionCount.textContent = count > 0 ? `已选择 1 个字段` : '请选择字段';
        }
    }
};

/**
 * @function FieldSelectorComponent.prototype.isFieldSelected
 * @description 检查字段是否已选择
 * @param {Object} field - 字段对象
 * @returns {boolean} 是否已选择
 */
FieldSelectorComponent.prototype.isFieldSelected = function(field) {
    return this.selectedFields.some(selected => selected.name === field.name);
};

/**
 * @function FieldSelectorComponent.prototype.confirm
 * @description 确认选择
 */
FieldSelectorComponent.prototype.confirm = function() {
    // 验证选择
    if (this.selectedFields.length === 0) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请至少选择一个字段', 'warning');
        return;
    }

    // 触发选择事件
    if (this.options.onSelect) {
        this.options.onSelect(this.selectedFields, this.options.fieldType);
    }

    this.eventBus.emit(SmartOffice.Events.FIELD_SELECTOR_CONFIRM, {
        fields: this.selectedFields,
        fieldType: this.options.fieldType
    });

    // 隐藏选择器
    this.hide();

    SmartOffice.log('info', '字段选择已确认:', this.selectedFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.cancel
 * @description 取消选择
 */
FieldSelectorComponent.prototype.cancel = function() {
    // 触发取消事件
    if (this.options.onCancel) {
        this.options.onCancel();
    }

    this.eventBus.emit(SmartOffice.Events.FIELD_SELECTOR_CANCEL, {
        fieldType: this.options.fieldType
    });

    // 隐藏选择器
    this.hide();

    SmartOffice.log('info', '字段选择已取消');
};

/**
 * @function FieldSelectorComponent.prototype.setFields
 * @description 设置可选字段
 * @param {Array} fields - 字段数组
 */
FieldSelectorComponent.prototype.setFields = function(fields) {
    this.options.fields = fields || [];
    this.processFields();

    // 重新渲染字段列表
    if (this.listElement) {
        this.listElement.innerHTML = this.renderFieldList();
        // 重新绑定字段列表的事件监听器
        this.bindFieldListEvents();
    }

    // 清空当前选择
    this.selectedFields = [];
    this.updateSelectionInfo();

    SmartOffice.log('info', '字段选择器字段已更新:', this.availableFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.getSelectedFields
 * @description 获取选中的字段
 * @returns {Array} 选中的字段数组
 */
FieldSelectorComponent.prototype.getSelectedFields = function() {
    return [...this.selectedFields];
};

/**
 * @function FieldSelectorComponent.prototype.setSelectedFields
 * @description 设置选中的字段
 * @param {Array} fields - 要选中的字段数组
 */
FieldSelectorComponent.prototype.setSelectedFields = function(fields) {
    this.selectedFields = fields ? [...fields] : [];
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.clearSelection
 * @description 清空选择
 */
FieldSelectorComponent.prototype.clearSelection = function() {
    this.selectedFields = [];
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.clearFields
 * @description 清空所有字段数据
 */
FieldSelectorComponent.prototype.clearFields = function() {
    // 清空字段数据
    this.availableFields = [];
    this.selectedFields = [];
    this.dataSource = null;

    // 清空容器内容
    if (this.containerElement) {
        this.containerElement.innerHTML = '';
    }

    // 重新渲染空状态
    this.render();

    SmartOffice.log('info', '字段选择器已清空');
};

/**
 * @function FieldSelectorComponent.prototype.destroy
 * @description 销毁组件
 */
FieldSelectorComponent.prototype.destroy = function() {
    try {
        // 隐藏选择器
        if (this.isVisible) {
            this.hide();
        }

        // 移除事件监听器
        const cancelButton = this.containerElement ? this.containerElement.querySelector('#cancelButton') : null;
        const confirmButton = this.containerElement ? this.containerElement.querySelector('#confirmButton') : null;

        if (cancelButton && this.cancelButtonHandler) {
            cancelButton.removeEventListener('click', this.cancelButtonHandler);
        }

        if (confirmButton && this.confirmButtonHandler) {
            confirmButton.removeEventListener('click', this.confirmButtonHandler);
        }

        if (this.backdropElement) {
            this.backdropElement.removeEventListener('click', this.cancel);
        }

        // 清空容器
        if (this.containerElement) {
            this.containerElement.innerHTML = '';
        }

        // 清理引用
        this.containerElement = null;
        this.modalElement = null;
        this.backdropElement = null;
        this.headerElement = null;
        this.listElement = null;
        this.footerElement = null;
        this.selectedFields = [];
        this.availableFields = [];

        SmartOffice.log('info', '字段选择器组件已销毁');

    } catch (error) {
        SmartOffice.log('error', '字段选择器组件销毁失败:', error);
    }
};

/**
 * @function FieldSelectorComponent.prototype.refresh
 * @description 刷新组件
 */
FieldSelectorComponent.prototype.refresh = function() {
    if (this.listElement) {
        this.listElement.innerHTML = this.renderFieldList();
        // 重新绑定字段列表的事件监听器
        this.bindFieldListEvents();
    }
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.isSelectorVisible
 * @description 检查选择器是否可见
 * @returns {boolean} 是否可见
 */
FieldSelectorComponent.prototype.isSelectorVisible = function() {
    return this.isVisible;
};

/**
 * @function FieldSelectorComponent.prototype.showTimeRangeModal
 * @description 显示时间区间设置模态框
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.showTimeRangeModal = function(field) {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    // 创建时间区间设置模态框
    this.createTimeRangeModal(field);

    SmartOffice.log('info', '显示时间区间设置模态框:', field.name);
};

/**
 * @function FieldSelectorComponent.prototype.showAggregationModal
 * @description 显示聚合方式设置模态框
 * @param {Object} field - 数值字段对象
 */
FieldSelectorComponent.prototype.showAggregationModal = function(field) {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    // 创建聚合方式设置模态框
    this.createAggregationModal(field);

    SmartOffice.log('info', '显示聚合方式设置模态框:', field.name);
};

/**
 * @function FieldSelectorComponent.prototype.createTimeRangeModal
 * @description 创建时间区间设置模态框
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.createTimeRangeModal = function(field) {
    // 创建模态框容器
    const modal = this.dom.createElement('div', {
        className: 'time-range-modal',
        id: 'timeRangeModal'
    });

    const modalContent = this.dom.createElement('div', {
        className: 'time-range-modal-content'
    });

    // 模态框头部
    const header = this.dom.createElement('div', {
        className: 'time-range-modal-header'
    });

    const title = this.dom.createElement('h3', {}, `设置时间区间 - ${field.name}`);
    const closeBtn = this.dom.createElement('button', {
        className: 'time-range-modal-close',
        type: 'button'
    }, '×');

    header.appendChild(title);
    header.appendChild(closeBtn);

    // 模态框主体
    const body = this.dom.createElement('div', {
        className: 'time-range-modal-body'
    });

    // 预设时间区间
    const presetsSection = this.createTimeRangePresets(field);
    body.appendChild(presetsSection);

    // 自定义时间区间
    const customSection = this.createCustomTimeRange(field);
    body.appendChild(customSection);

    // 已设置的时间区间列表
    const rangesSection = this.createTimeRangesList(field);
    body.appendChild(rangesSection);

    // 模态框底部
    const footer = this.dom.createElement('div', {
        className: 'time-range-modal-footer'
    });

    const cancelBtn = this.dom.createElement('button', {
        className: 'ios-button ios-button-secondary',
        type: 'button',
        id: 'timeRangeCancelBtn'
    }, '取消');

    const confirmBtn = this.dom.createElement('button', {
        className: 'ios-button ios-button-primary',
        type: 'button',
        id: 'timeRangeConfirmBtn'
    }, '确定');

    footer.appendChild(cancelBtn);
    footer.appendChild(confirmBtn);

    // 组装模态框
    modalContent.appendChild(header);
    modalContent.appendChild(body);
    modalContent.appendChild(footer);
    modal.appendChild(modalContent);

    // 添加到页面
    document.body.appendChild(modal);

    // 绑定事件
    this.bindTimeRangeModalEvents(modal, field);

    // 显示模态框
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
};

/**
 * @function FieldSelectorComponent.prototype.createAggregationModal
 * @description 创建聚合方式设置模态框
 * @param {Object} field - 数值字段对象
 */
FieldSelectorComponent.prototype.createAggregationModal = function(field) {
    // 创建模态框容器
    const modal = this.dom.createElement('div', {
        className: 'aggregation-modal',
        id: 'aggregationModal'
    });

    const modalContent = this.dom.createElement('div', {
        className: 'aggregation-modal-content'
    });

    // 模态框头部
    const header = this.dom.createElement('div', {
        className: 'aggregation-modal-header'
    });

    const title = this.dom.createElement('h3', {}, `设置聚合方式 - ${field.name}`);
    const closeBtn = this.dom.createElement('button', {
        className: 'aggregation-modal-close',
        type: 'button'
    }, '×');

    header.appendChild(title);
    header.appendChild(closeBtn);

    // 模态框主体
    const body = this.dom.createElement('div', {
        className: 'aggregation-modal-body'
    });

    // 聚合方式选择
    const aggregationSection = this.createAggregationOptions(field);
    body.appendChild(aggregationSection);

    // 已选择的聚合方式列表
    const selectedSection = this.createSelectedAggregationsList(field);
    body.appendChild(selectedSection);

    // 模态框底部
    const footer = this.dom.createElement('div', {
        className: 'aggregation-modal-footer'
    });

    const cancelBtn = this.dom.createElement('button', {
        className: 'ios-button ios-button-secondary',
        type: 'button',
        id: 'aggregationCancelBtn'
    }, '取消');

    const confirmBtn = this.dom.createElement('button', {
        className: 'ios-button ios-button-primary',
        type: 'button',
        id: 'aggregationConfirmBtn'
    }, '确定');

    footer.appendChild(cancelBtn);
    footer.appendChild(confirmBtn);

    // 组装模态框
    modalContent.appendChild(header);
    modalContent.appendChild(body);
    modalContent.appendChild(footer);
    modal.appendChild(modalContent);

    // 添加到页面
    document.body.appendChild(modal);

    // 绑定事件
    this.bindAggregationModalEvents(modal, field);

    // 显示模态框
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
};

/**
 * @function FieldSelectorComponent.prototype.createTimeRangePresets
 * @description 创建预设时间区间选择
 * @param {Object} field - 时间字段对象
 * @returns {HTMLElement} 预设区间元素
 */
FieldSelectorComponent.prototype.createTimeRangePresets = function(field) {
    const section = this.dom.createElement('div', {
        className: 'time-range-presets'
    });

    const title = this.dom.createElement('h4', {}, '常用时间区间');
    section.appendChild(title);

    const presets = [
        { name: '上午', start: '09:00', end: '12:00' },
        { name: '下午', start: '13:00', end: '18:00' },
        { name: '晚上', start: '19:00', end: '23:00' },
        { name: '全天', start: '00:00', end: '23:59' },
        { name: '工作时间', start: '09:00', end: '18:00' },
        { name: '营业时间', start: '08:00', end: '22:00' }
    ];

    const presetsGrid = this.dom.createElement('div', {
        className: 'time-range-presets-grid'
    });

    for (let i = 0; i < presets.length; i++) {
        const preset = presets[i];
        const presetBtn = this.dom.createElement('button', {
            className: 'time-range-preset-btn',
            type: 'button',
            dataset: {
                name: preset.name,
                start: preset.start,
                end: preset.end
            }
        }, `${preset.name}<br><small>${preset.start}-${preset.end}</small>`);

        presetsGrid.appendChild(presetBtn);
    }

    section.appendChild(presetsGrid);
    return section;
};

/**
 * @function FieldSelectorComponent.prototype.createCustomTimeRange
 * @description 创建自定义时间区间输入
 * @param {Object} field - 时间字段对象
 * @returns {HTMLElement} 自定义区间元素
 */
FieldSelectorComponent.prototype.createCustomTimeRange = function(field) {
    const section = this.dom.createElement('div', {
        className: 'time-range-custom'
    });

    const title = this.dom.createElement('h4', {}, '自定义时间区间');
    section.appendChild(title);

    const form = this.dom.createElement('div', {
        className: 'time-range-form'
    });

    // 区间名称
    const nameRow = this.dom.createElement('div', {
        className: 'time-range-form-row'
    });
    const nameLabel = this.dom.createElement('label', {}, '区间名称');
    const nameInput = this.dom.createElement('input', {
        type: 'text',
        className: 'ios-form-input',
        id: 'customRangeName',
        placeholder: '例如：午休时间'
    });
    nameRow.appendChild(nameLabel);
    nameRow.appendChild(nameInput);

    // 开始时间
    const startRow = this.dom.createElement('div', {
        className: 'time-range-form-row'
    });
    const startLabel = this.dom.createElement('label', {}, '开始时间');
    const startInput = this.dom.createElement('input', {
        type: 'time',
        className: 'ios-form-input',
        id: 'customRangeStart'
    });
    startRow.appendChild(startLabel);
    startRow.appendChild(startInput);

    // 结束时间
    const endRow = this.dom.createElement('div', {
        className: 'time-range-form-row'
    });
    const endLabel = this.dom.createElement('label', {}, '结束时间');
    const endInput = this.dom.createElement('input', {
        type: 'time',
        className: 'ios-form-input',
        id: 'customRangeEnd'
    });
    endRow.appendChild(endLabel);
    endRow.appendChild(endInput);

    // 添加按钮
    const addBtn = this.dom.createElement('button', {
        className: 'ios-button ios-button-secondary',
        type: 'button',
        id: 'addCustomRangeBtn'
    }, '添加区间');

    form.appendChild(nameRow);
    form.appendChild(startRow);
    form.appendChild(endRow);
    form.appendChild(addBtn);

    section.appendChild(form);
    return section;
};

/**
 * @function FieldSelectorComponent.prototype.createTimeRangesList
 * @description 创建已设置的时间区间列表
 * @param {Object} field - 时间字段对象
 * @returns {HTMLElement} 区间列表元素
 */
FieldSelectorComponent.prototype.createTimeRangesList = function(field) {
    const section = this.dom.createElement('div', {
        className: 'time-ranges-list'
    });

    const title = this.dom.createElement('h4', {}, '已设置的时间区间');
    section.appendChild(title);

    const list = this.dom.createElement('div', {
        className: 'time-ranges-items',
        id: 'timeRangesList'
    });

    // 显示现有的时间区间
    this.updateTimeRangesList(field, list);

    section.appendChild(list);
    return section;
};

/**
 * @function FieldSelectorComponent.prototype.bindTimeRangeModalEvents
 * @description 绑定时间区间模态框事件
 * @param {HTMLElement} modal - 模态框元素
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.bindTimeRangeModalEvents = function(modal, field) {
    const self = this;

    // 关闭按钮事件
    const closeBtn = modal.querySelector('.time-range-modal-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            self.closeTimeRangeModal();
        });
    }

    // 取消按钮事件
    const cancelBtn = modal.querySelector('#timeRangeCancelBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            self.closeTimeRangeModal();
        });
    }

    // 确定按钮事件
    const confirmBtn = modal.querySelector('#timeRangeConfirmBtn');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            self.confirmTimeRanges(field);
        });
    }

    // 预设按钮事件
    const presetBtns = modal.querySelectorAll('.time-range-preset-btn');
    for (let i = 0; i < presetBtns.length; i++) {
        presetBtns[i].addEventListener('click', function() {
            self.addPresetTimeRange(field, this.dataset);
        });
        this.dom.addTouchFeedback(presetBtns[i], 'light');
    }

    // 添加自定义区间按钮事件
    const addCustomBtn = modal.querySelector('#addCustomRangeBtn');
    if (addCustomBtn) {
        addCustomBtn.addEventListener('click', function() {
            self.addCustomTimeRange(field);
        });
    }

    // 背景点击关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            self.closeTimeRangeModal();
        }
    });
};

/**
 * @function FieldSelectorComponent.prototype.addPresetTimeRange
 * @description 添加预设时间区间
 * @param {Object} field - 时间字段对象
 * @param {Object} preset - 预设数据
 */
FieldSelectorComponent.prototype.addPresetTimeRange = function(field, preset) {
    if (!field.timeRanges) {
        field.timeRanges = [];
    }

    // 检查是否已存在相同的区间
    const exists = field.timeRanges.some(range =>
        range.start === preset.start && range.end === preset.end
    );

    if (exists) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '该时间区间已存在', 'warning');
        return;
    }

    // 添加新区间
    field.timeRanges.push({
        name: preset.name,
        start: preset.start,
        end: preset.end,
        type: 'preset'
    });

    // 更新列表显示
    const list = document.getElementById('timeRangesList');
    if (list) {
        this.updateTimeRangesList(field, list);
    }

    SmartOffice.log('info', '已添加预设时间区间:', preset.name);
};

/**
 * @function FieldSelectorComponent.prototype.addCustomTimeRange
 * @description 添加自定义时间区间
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.addCustomTimeRange = function(field) {
    const nameInput = document.getElementById('customRangeName');
    const startInput = document.getElementById('customRangeStart');
    const endInput = document.getElementById('customRangeEnd');

    if (!nameInput || !startInput || !endInput) {
        return;
    }

    const name = nameInput.value.trim();
    const start = startInput.value;
    const end = endInput.value;

    // 验证输入
    if (!name) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请输入区间名称', 'warning');
        return;
    }

    if (!start || !end) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请选择开始和结束时间', 'warning');
        return;
    }

    if (start >= end) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '开始时间必须早于结束时间', 'warning');
        return;
    }

    if (!field.timeRanges) {
        field.timeRanges = [];
    }

    // 检查是否已存在相同的区间
    const exists = field.timeRanges.some(range =>
        range.start === start && range.end === end
    );

    if (exists) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '该时间区间已存在', 'warning');
        return;
    }

    // 添加新区间
    field.timeRanges.push({
        name: name,
        start: start,
        end: end,
        type: 'custom'
    });

    // 清空输入框
    nameInput.value = '';
    startInput.value = '';
    endInput.value = '';

    // 更新列表显示
    const list = document.getElementById('timeRangesList');
    if (list) {
        this.updateTimeRangesList(field, list);
    }

    SmartOffice.log('info', '已添加自定义时间区间:', name);
};

/**
 * @function FieldSelectorComponent.prototype.updateTimeRangesList
 * @description 更新时间区间列表显示
 * @param {Object} field - 时间字段对象
 * @param {HTMLElement} listElement - 列表容器元素
 */
FieldSelectorComponent.prototype.updateTimeRangesList = function(field, listElement) {
    if (!listElement) return;

    listElement.innerHTML = '';

    if (!field.timeRanges || field.timeRanges.length === 0) {
        listElement.innerHTML = '<div class="time-ranges-empty">暂无设置的时间区间</div>';
        return;
    }

    for (let i = 0; i < field.timeRanges.length; i++) {
        const range = field.timeRanges[i];
        const item = this.dom.createElement('div', {
            className: 'time-range-item'
        });

        const info = this.dom.createElement('div', {
            className: 'time-range-info'
        });

        const name = this.dom.createElement('span', {
            className: 'time-range-name'
        }, range.name);

        const time = this.dom.createElement('span', {
            className: 'time-range-time'
        }, `${range.start} - ${range.end}`);

        const removeBtn = this.dom.createElement('button', {
            className: 'time-range-remove',
            type: 'button',
            dataset: { index: i }
        }, '×');

        info.appendChild(name);
        info.appendChild(time);
        item.appendChild(info);
        item.appendChild(removeBtn);

        listElement.appendChild(item);

        // 绑定删除事件
        const self = this;
        removeBtn.addEventListener('click', function() {
            self.removeTimeRange(field, parseInt(this.dataset.index));
        });
    }
};

/**
 * @function FieldSelectorComponent.prototype.removeTimeRange
 * @description 移除时间区间
 * @param {Object} field - 时间字段对象
 * @param {number} index - 区间索引
 */
FieldSelectorComponent.prototype.removeTimeRange = function(field, index) {
    if (!field.timeRanges || index < 0 || index >= field.timeRanges.length) {
        return;
    }

    field.timeRanges.splice(index, 1);

    // 更新列表显示
    const list = document.getElementById('timeRangesList');
    if (list) {
        this.updateTimeRangesList(field, list);
    }

    SmartOffice.log('info', '已移除时间区间，索引:', index);
};

/**
 * @function FieldSelectorComponent.prototype.confirmTimeRanges
 * @description 确认时间区间设置
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.confirmTimeRanges = function(field) {
    // 更新字段选择器显示
    this.refresh();

    // 关闭模态框
    this.closeTimeRangeModal();

    // 触发时间区间更新事件
    this.eventBus.emit(SmartOffice.Events.TIME_RANGE_UPDATED, {
        field: field,
        timeRanges: field.timeRanges || []
    });

    SmartOffice.log('info', '时间区间设置已确认:', field.name, field.timeRanges);
};

/**
 * @function FieldSelectorComponent.prototype.closeTimeRangeModal
 * @description 关闭时间区间设置模态框
 */
FieldSelectorComponent.prototype.closeTimeRangeModal = function() {
    const modal = document.getElementById('timeRangeModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
};

/**
 * @function FieldSelectorComponent.prototype.createAggregationOptions
 * @description 创建聚合方式选择选项
 * @param {Object} field - 数值字段对象
 * @returns {HTMLElement} 聚合选项元素
 */
FieldSelectorComponent.prototype.createAggregationOptions = function(field) {
    const section = this.dom.createElement('div', {
        className: 'aggregation-options'
    });

    const title = this.dom.createElement('h4', {}, '选择聚合方式');
    section.appendChild(title);

    const aggregationTypes = [
        { type: 'sum', label: '求和', description: '计算所有数值的总和' },
        { type: 'average', label: '平均值', description: '计算所有数值的平均值' },
        { type: 'count', label: '计数', description: '计算非空值的数量' },
        { type: 'max', label: '最大值', description: '找出最大的数值' },
        { type: 'min', label: '最小值', description: '找出最小的数值' },
        { type: 'countDistinct', label: '去重计数', description: '计算不重复值的数量' }
    ];

    const optionsGrid = this.dom.createElement('div', {
        className: 'aggregation-options-grid'
    });

    for (let i = 0; i < aggregationTypes.length; i++) {
        const aggType = aggregationTypes[i];
        const isSelected = field.aggregations && field.aggregations.includes(aggType.type);

        const optionBtn = this.dom.createElement('button', {
            className: `aggregation-option-btn ${isSelected ? 'selected' : ''}`,
            type: 'button',
            dataset: {
                aggregationType: aggType.type
            }
        });

        optionBtn.innerHTML = `
            <div class="aggregation-option-content">
                <span class="aggregation-option-label">${aggType.label}</span>
                <span class="aggregation-option-description">${aggType.description}</span>
            </div>
            <div class="aggregation-option-checkbox ${isSelected ? 'checked' : ''}">
                <svg viewBox="0 0 24 24" class="checkbox-icon">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
            </div>
        `;

        optionsGrid.appendChild(optionBtn);
    }

    section.appendChild(optionsGrid);
    return section;
};

/**
 * @function FieldSelectorComponent.prototype.createSelectedAggregationsList
 * @description 创建已选择的聚合方式列表
 * @param {Object} field - 数值字段对象
 * @returns {HTMLElement} 已选择列表元素
 */
FieldSelectorComponent.prototype.createSelectedAggregationsList = function(field) {
    const section = this.dom.createElement('div', {
        className: 'selected-aggregations'
    });

    const title = this.dom.createElement('h4', {}, '已选择的聚合方式');
    section.appendChild(title);

    const listContainer = this.dom.createElement('div', {
        className: 'selected-aggregations-list',
        id: 'selectedAggregationsList'
    });

    this.updateSelectedAggregationsList(listContainer, field);
    section.appendChild(listContainer);

    return section;
};

/**
 * @function FieldSelectorComponent.prototype.updateSelectedAggregationsList
 * @description 更新已选择的聚合方式列表
 * @param {HTMLElement} container - 列表容器
 * @param {Object} field - 数值字段对象
 */
FieldSelectorComponent.prototype.updateSelectedAggregationsList = function(container, field) {
    container.innerHTML = '';

    if (!field.aggregations || field.aggregations.length === 0) {
        container.innerHTML = '<div class="aggregations-empty">暂未选择聚合方式</div>';
        return;
    }

    for (let i = 0; i < field.aggregations.length; i++) {
        const aggType = field.aggregations[i];
        const label = this.getAggregationLabel(aggType);

        const item = this.dom.createElement('div', {
            className: 'selected-aggregation-item'
        });

        item.innerHTML = `
            <div class="aggregation-info">
                <span class="aggregation-name">${label}</span>
                <span class="aggregation-result-name">${field.name}_${label}</span>
            </div>
            <button type="button" class="aggregation-remove" data-aggregation-type="${aggType}">
                ×
            </button>
        `;

        container.appendChild(item);
    }
};

/**
 * @function FieldSelectorComponent.prototype.bindAggregationModalEvents
 * @description 绑定聚合方式模态框事件
 * @param {HTMLElement} modal - 模态框元素
 * @param {Object} field - 数值字段对象
 */
FieldSelectorComponent.prototype.bindAggregationModalEvents = function(modal, field) {
    const self = this;

    // 关闭按钮事件
    const closeBtn = modal.querySelector('.aggregation-modal-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            self.closeAggregationModal();
        });
    }

    // 取消按钮事件
    const cancelBtn = modal.querySelector('#aggregationCancelBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            self.closeAggregationModal();
        });
    }

    // 确定按钮事件
    const confirmBtn = modal.querySelector('#aggregationConfirmBtn');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            self.confirmAggregations(field);
        });
    }

    // 聚合选项点击事件
    const optionBtns = modal.querySelectorAll('.aggregation-option-btn');
    for (let i = 0; i < optionBtns.length; i++) {
        const btn = optionBtns[i];
        btn.addEventListener('click', function() {
            self.toggleAggregationOption(field, this.dataset.aggregationType);
        });
    }

    // 背景点击关闭
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            self.closeAggregationModal();
        }
    });
};

/**
 * @function FieldSelectorComponent.prototype.toggleAggregationOption
 * @description 切换聚合选项
 * @param {Object} field - 数值字段对象
 * @param {string} aggregationType - 聚合类型
 */
FieldSelectorComponent.prototype.toggleAggregationOption = function(field, aggregationType) {
    if (!field.aggregations) {
        field.aggregations = [];
    }

    const index = field.aggregations.indexOf(aggregationType);
    if (index > -1) {
        // 移除聚合类型
        field.aggregations.splice(index, 1);
    } else {
        // 添加聚合类型
        field.aggregations.push(aggregationType);
    }

    // 更新界面显示
    const modal = document.getElementById('aggregationModal');
    if (modal) {
        const optionBtn = modal.querySelector(`[data-aggregation-type="${aggregationType}"]`);
        const checkbox = optionBtn.querySelector('.aggregation-option-checkbox');

        if (index > -1) {
            // 取消选择
            optionBtn.classList.remove('selected');
            checkbox.classList.remove('checked');
        } else {
            // 选择
            optionBtn.classList.add('selected');
            checkbox.classList.add('checked');
        }

        // 更新已选择列表
        const listContainer = modal.querySelector('#selectedAggregationsList');
        if (listContainer) {
            this.updateSelectedAggregationsList(listContainer, field);
        }
    }

    SmartOffice.log('info', '聚合选项已切换:', aggregationType, field.aggregations);
};

/**
 * @function FieldSelectorComponent.prototype.confirmAggregations
 * @description 确认聚合方式设置
 * @param {Object} field - 数值字段对象
 */
FieldSelectorComponent.prototype.confirmAggregations = function(field) {
    // 更新字段选择器显示
    this.refresh();

    // 关闭模态框
    this.closeAggregationModal();

    // 触发聚合方式更新事件
    this.eventBus.emit(SmartOffice.Events.AGGREGATION_UPDATED, {
        field: field,
        aggregations: field.aggregations || []
    });

    SmartOffice.log('info', '聚合方式设置已确认:', field.name, field.aggregations);
};

/**
 * @function FieldSelectorComponent.prototype.closeAggregationModal
 * @description 关闭聚合方式设置模态框
 */
FieldSelectorComponent.prototype.closeAggregationModal = function() {
    const modal = document.getElementById('aggregationModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
};

// 注册到全局命名空间
SmartOffice.Components.FieldSelector = FieldSelectorComponent;

SmartOffice.log('info', 'SmartOffice字段选择器组件模块初始化完成');
