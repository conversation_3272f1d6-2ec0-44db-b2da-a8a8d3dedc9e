/**
 * @file 字段选择器组件样式
 * @description iOS风格的字段选择器模态框样式
 * <AUTHOR> Team
 * @version 1.0.0
 */

/* 字段选择器全局容器 */
.field-selector-global-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    pointer-events: none;
}

.field-selector-global-container.active {
    pointer-events: auto;
}

/* 背景遮罩 */
.so-field-selector-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    opacity: 0;
    transition: opacity var(--animation-standard) ease;
    z-index: 10000;
    pointer-events: auto;
}

.so-field-selector-backdrop.so-backdrop-visible {
    opacity: 1;
}

/* 模态框容器 */
.so-field-selector-modal {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--background-primary);
    border-radius: var(--radius-large) var(--radius-large) 0 0;
    max-height: 80vh;
    transform: translateY(100%);
    transition: transform var(--animation-standard) ease;
    z-index: 10001;
    pointer-events: auto;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
}

.so-field-selector-modal.so-modal-visible {
    transform: translateY(0);
}

/* 模态框头部 */
.so-field-selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--separator-opaque);
    background-color: var(--background-secondary);
    border-radius: var(--radius-large) var(--radius-large) 0 0;
}

.so-header-title {
    font-size: var(--font-size-headline);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
    text-align: center;
    flex: 1;
}

.so-header-button {
    background: none;
    border: none;
    font-size: var(--font-size-body);
    color: var(--ios-blue);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    min-height: 44px;
    min-width: 60px;
}

.so-header-button:hover {
    background-color: var(--background-tertiary);
}

.so-header-button:active {
    transform: scale(0.95);
    background-color: var(--text-quaternary);
}

.so-header-button.cancel {
    color: var(--text-secondary);
}

.so-header-button.confirm {
    font-weight: var(--font-weight-semibold);
}

/* 字段列表内容区域 */
.so-field-selector-content {
    max-height: 50vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.so-field-list {
    padding: var(--spacing-sm) 0;
}

/* 字段项 */
.so-field-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--separator-non-opaque);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    min-height: 60px;
}

.so-field-item:hover {
    background-color: var(--background-tertiary);
}

.so-field-item:active {
    background-color: var(--text-quaternary);
    transform: scale(0.98);
}

.so-field-item.selected {
    background-color: var(--ios-blue-light);
}

.so-field-item.selected:hover {
    background-color: var(--ios-blue-light);
}

/* 映射字段样式 */
.so-field-item.mapped-field {
    border-left: 4px solid var(--ios-green);
    background-color: var(--ios-green-light);
}

.so-field-item.mapped-field:hover {
    background-color: var(--ios-green-light);
    border-color: var(--ios-green);
}

.so-field-item.mapped-field.selected {
    background-color: var(--ios-green);
    border-left-color: var(--ios-green-dark);
}

.so-field-mapped-indicator {
    display: inline-flex;
    align-items: center;
    margin-left: var(--spacing-xs);
    padding: 2px 4px;
    background-color: var(--ios-green);
    border-radius: var(--radius-small);
}

.so-field-mapped-indicator .mapped-icon {
    width: 12px;
    height: 12px;
    fill: white;
}

.so-field-item.mapped-field .so-field-name {
    font-weight: var(--font-weight-semibold);
    color: var(--ios-green-dark);
}

.so-field-item.mapped-field .so-field-description {
    color: var(--ios-green-dark);
    font-style: italic;
}

/* 字段内容 */
.so-field-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.so-field-main {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.so-field-name {
    font-size: var(--font-size-body);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.so-field-description {
    font-size: var(--font-size-caption);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* 字段类型标签 */
.so-field-type {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    border-radius: var(--radius-small);
    font-size: var(--font-size-caption);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.so-field-type-number {
    background-color: var(--ios-green-light);
    color: var(--ios-green);
}

.so-field-type-date {
    background-color: var(--ios-orange-light);
    color: var(--ios-orange);
}

.so-field-type-time {
    background-color: var(--ios-purple-light);
    color: var(--ios-purple);
}

.so-field-type-string,
.so-field-type-text {
    background-color: var(--text-quaternary);
    color: var(--text-secondary);
}

/* 时间区间按钮 */
.so-time-range-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: var(--ios-purple);
    border: none;
    border-radius: var(--radius-small);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
}

.so-time-range-btn:hover {
    background-color: var(--ios-purple-dark);
}

.so-time-range-btn:active {
    transform: scale(0.9);
}

.time-range-icon {
    width: 14px;
    height: 14px;
    fill: white;
}

/* 时间区间显示 */
.so-time-ranges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-xs);
}

.so-time-range-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    background-color: var(--ios-purple-light);
    color: var(--ios-purple);
    border-radius: var(--radius-small);
    font-size: var(--font-size-caption);
    font-weight: var(--font-weight-medium);
}

/* 字段选择器 */
.so-field-selector {
    margin-left: var(--spacing-md);
}

/* 复选框样式 */
.so-field-checkbox {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: 2px solid var(--text-tertiary);
    border-radius: var(--radius-small);
    background-color: transparent;
    transition: all var(--animation-fast) ease;
}

.so-field-checkbox.checked {
    background-color: var(--ios-blue);
    border-color: var(--ios-blue);
}

.checkbox-icon {
    width: 16px;
    height: 16px;
    fill: white;
    opacity: 0;
    transition: opacity var(--animation-fast) ease;
}

.so-field-checkbox.checked .checkbox-icon {
    opacity: 1;
}

/* 单选框样式 */
.so-field-radio {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: 2px solid var(--text-tertiary);
    border-radius: 50%;
    background-color: transparent;
    transition: all var(--animation-fast) ease;
}

.so-field-radio.checked {
    border-color: var(--ios-blue);
}

.so-radio-inner {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--ios-blue);
    transform: scale(0);
    transition: transform var(--animation-fast) ease;
}

.so-field-radio.checked .so-radio-inner {
    transform: scale(1);
}

/* 底部信息 */
.so-field-selector-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--separator-opaque);
    background-color: var(--background-secondary);
}

.so-selection-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
}

.so-max-info {
    color: var(--text-tertiary);
}

/* 空状态 */
.so-field-empty {
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-secondary);
    font-size: var(--font-size-body);
}

/* 防止背景滚动 */
.so-modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* 时间区间模态框 */
.time-range-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10002;
    opacity: 0;
    visibility: hidden;
    transition: all var(--animation-standard) ease;
}

.time-range-modal.show {
    opacity: 1;
    visibility: visible;
}

.time-range-modal-content {
    background-color: var(--background-primary);
    border-radius: var(--radius-large);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--animation-standard) ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.time-range-modal.show .time-range-modal-content {
    transform: scale(1);
}

/* 时间区间模态框头部 */
.time-range-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--separator-opaque);
    background-color: var(--background-secondary);
}

.time-range-modal-header h3 {
    margin: 0;
    font-size: var(--font-size-headline);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    flex: 1;
}

.time-range-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-small);
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.time-range-modal-close:hover {
    background-color: var(--background-tertiary);
}

/* 时间区间模态框主体 */
.time-range-modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.time-range-modal-body h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-subhead);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

/* 预设时间区间 */
.time-range-presets {
    margin-bottom: var(--spacing-xl);
}

.time-range-presets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.time-range-preset-btn {
    padding: var(--spacing-md);
    background-color: var(--background-tertiary);
    border: 1px solid var(--separator-opaque);
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    text-align: center;
    font-size: var(--font-size-footnote);
    color: var(--text-primary);
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.time-range-preset-btn:hover {
    background-color: var(--ios-blue-light);
    border-color: var(--ios-blue);
}

.time-range-preset-btn:active {
    transform: scale(0.95);
}

.time-range-preset-btn small {
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* 自定义时间区间 */
.time-range-custom {
    margin-bottom: var(--spacing-xl);
}

.time-range-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.time-range-form-row {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.time-range-form-row label {
    font-size: var(--font-size-footnote);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.ios-form-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--separator-opaque);
    border-radius: var(--radius-medium);
    background-color: var(--background-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-body);
    transition: all var(--animation-fast) ease;
}

.ios-form-input:focus {
    outline: none;
    border-color: var(--ios-blue);
    background-color: var(--background-primary);
}

/* 已设置的时间区间列表 */
.time-ranges-list {
    margin-bottom: var(--spacing-lg);
}

.time-ranges-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.time-range-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--background-tertiary);
    border-radius: var(--radius-medium);
    border: 1px solid var(--separator-opaque);
}

.time-range-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.time-range-name {
    font-size: var(--font-size-footnote);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.time-range-time {
    font-size: var(--font-size-caption);
    color: var(--text-secondary);
}

.time-range-remove {
    background: none;
    border: none;
    color: var(--ios-red);
    font-size: 18px;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-small);
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.time-range-remove:hover {
    background-color: var(--ios-red-light);
}

.time-ranges-empty {
    padding: var(--spacing-lg);
    text-align: center;
    color: var(--text-secondary);
    font-size: var(--font-size-footnote);
}

/* 时间区间模态框底部 */
.time-range-modal-footer {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--separator-opaque);
    background-color: var(--background-secondary);
}

.ios-button {
    flex: 1;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-medium);
    font-size: var(--font-size-body);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    min-height: 44px;
}

.ios-button-primary {
    background-color: var(--ios-blue);
    color: white;
}

.ios-button-primary:hover {
    background-color: var(--ios-blue-dark);
}

.ios-button-secondary {
    background-color: var(--background-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--separator-opaque);
}

.ios-button-secondary:hover {
    background-color: var(--text-quaternary);
}

.ios-button:active {
    transform: scale(0.95);
}

/* 聚合方式按钮 */
.so-aggregation-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: var(--ios-orange);
    border: none;
    border-radius: var(--radius-small);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
}

.so-aggregation-btn:hover {
    background-color: var(--ios-orange-dark);
}

.so-aggregation-btn:active {
    transform: scale(0.9);
}

.aggregation-icon {
    width: 14px;
    height: 14px;
    fill: white;
}

/* 聚合方式显示 */
.so-aggregations {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-xs);
}

.so-aggregation-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    background-color: var(--ios-orange-light);
    color: var(--ios-orange);
    border-radius: var(--radius-small);
    font-size: var(--font-size-caption);
    font-weight: var(--font-weight-medium);
}

/* 聚合方式模态框 */
.aggregation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10002;
    opacity: 0;
    visibility: hidden;
    transition: all var(--animation-standard) ease;
}

.aggregation-modal.show {
    opacity: 1;
    visibility: visible;
}

.aggregation-modal-content {
    background-color: var(--background-primary);
    border-radius: var(--radius-large);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--animation-standard) ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.aggregation-modal.show .aggregation-modal-content {
    transform: scale(1);
}

/* 聚合方式模态框头部 */
.aggregation-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--separator-opaque);
    background-color: var(--background-secondary);
}

.aggregation-modal-header h3 {
    margin: 0;
    font-size: var(--font-size-headline);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    flex: 1;
}

.aggregation-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-small);
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.aggregation-modal-close:hover {
    background-color: var(--background-tertiary);
}

/* 聚合方式模态框主体 */
.aggregation-modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.aggregation-modal-body h4 {
    margin: 0 0 var(--spacing-md) 0;
    font-size: var(--font-size-subhead);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

/* 聚合选项 */
.aggregation-options {
    margin-bottom: var(--spacing-xl);
}

.aggregation-options-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
}

.aggregation-option-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background-color: var(--background-tertiary);
    border: 1px solid var(--separator-opaque);
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    text-align: left;
    min-height: 60px;
}

.aggregation-option-btn:hover {
    background-color: var(--ios-orange-light);
    border-color: var(--ios-orange);
}

.aggregation-option-btn:active {
    transform: scale(0.98);
}

.aggregation-option-btn.selected {
    background-color: var(--ios-orange-light);
    border-color: var(--ios-orange);
}

.aggregation-option-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.aggregation-option-label {
    font-size: var(--font-size-body);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.aggregation-option-description {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
}

.aggregation-option-checkbox {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: 2px solid var(--text-tertiary);
    border-radius: var(--radius-small);
    background-color: transparent;
    transition: all var(--animation-fast) ease;
}

.aggregation-option-checkbox.checked {
    background-color: var(--ios-orange);
    border-color: var(--ios-orange);
}

.aggregation-option-checkbox .checkbox-icon {
    width: 16px;
    height: 16px;
    fill: white;
    opacity: 0;
    transition: opacity var(--animation-fast) ease;
}

.aggregation-option-checkbox.checked .checkbox-icon {
    opacity: 1;
}

/* 已选择的聚合方式 */
.selected-aggregations {
    margin-bottom: var(--spacing-lg);
}

.selected-aggregations-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.selected-aggregation-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--background-tertiary);
    border-radius: var(--radius-medium);
    border: 1px solid var(--separator-opaque);
}

.aggregation-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.aggregation-name {
    font-size: var(--font-size-footnote);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.aggregation-result-name {
    font-size: var(--font-size-caption);
    color: var(--text-secondary);
    font-family: monospace;
}

.aggregation-remove {
    background: none;
    border: none;
    color: var(--ios-red);
    font-size: 18px;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-small);
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.aggregation-remove:hover {
    background-color: var(--ios-red-light);
}

.aggregations-empty {
    padding: var(--spacing-lg);
    text-align: center;
    color: var(--text-secondary);
    font-size: var(--font-size-footnote);
}

/* 聚合方式模态框底部 */
.aggregation-modal-footer {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--separator-opaque);
    background-color: var(--background-secondary);
}
