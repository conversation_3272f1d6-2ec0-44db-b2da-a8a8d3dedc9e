/**
 * @file 聚合模式管理面板样式
 * @description iOS Human Interface Guidelines兼容的聚合模式管理面板样式
 * <AUTHOR> Team
 * @version 1.0.0
 */

/* ==================== 聚合模式面板基础样式 ==================== */

.aggregation-panel {
    position: fixed;
    top: var(--nav-bar-height, 60px);
    right: 0;
    width: 320px;
    max-width: 90vw;
    height: calc(100vh - var(--nav-bar-height, 60px));
    background: var(--ios-background-primary);
    border-left: 1px solid var(--ios-separator-color);
    box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.aggregation-panel[style*="display: block"] {
    transform: translateX(0);
}

/* ==================== 面板头部 ==================== */

.aggregation-panel .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: var(--ios-background-secondary);
    border-bottom: 1px solid var(--ios-separator-color);
    min-height: 60px;
}

.aggregation-panel .panel-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--ios-text-primary);
    margin: 0;
}

.aggregation-panel .panel-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ios-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.aggregation-panel .panel-close:hover {
    background: var(--ios-background-tertiary);
    color: var(--ios-text-primary);
}

.aggregation-panel .panel-close:active {
    transform: scale(0.95);
    background: var(--ios-background-quaternary);
}

.aggregation-panel .panel-close svg {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* ==================== 面板内容 ==================== */

.aggregation-panel .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    -webkit-overflow-scrolling: touch;
}

.aggregation-content {
    padding: 20px;
}

/* ==================== 区域标签 ==================== */

.section-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--ios-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 12px;
    padding: 0 4px;
}

/* ==================== 当前聚合配置 ==================== */

.current-aggregations {
    margin-bottom: 32px;
}

.current-aggregations-list {
    background: var(--ios-background-secondary);
    border-radius: 12px;
    overflow: hidden;
}

.no-aggregations {
    padding: 24px;
    text-align: center;
    color: var(--ios-text-tertiary);
    font-size: 16px;
}

.aggregation-field-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--ios-separator-color);
    background: var(--ios-background-secondary);
    transition: background-color 0.2s ease;
}

.aggregation-field-item:last-child {
    border-bottom: none;
}

.aggregation-field-item:hover {
    background: var(--ios-background-tertiary);
}

.field-info {
    flex: 1;
    min-width: 0;
}

.field-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--ios-text-primary);
    display: block;
    margin-bottom: 4px;
}

.field-aggregations {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.aggregation-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: var(--ios-blue);
    color: white;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.remove-field-aggregation {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ios-red);
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 12px;
}

.remove-field-aggregation:hover {
    background: rgba(255, 59, 48, 0.1);
}

.remove-field-aggregation:active {
    transform: scale(0.95);
}

.remove-field-aggregation svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* ==================== 添加聚合配置 ==================== */

.add-aggregation {
    margin-bottom: 32px;
}

.add-aggregation-form {
    background: var(--ios-background-secondary);
    border-radius: 12px;
    padding: 20px;
}

.form-row {
    margin-bottom: 20px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-row label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--ios-text-primary);
    margin-bottom: 8px;
}

.ios-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--ios-separator-color);
    border-radius: 8px;
    background: var(--ios-background-primary);
    color: var(--ios-text-primary);
    font-size: 16px;
    transition: border-color 0.2s ease;
}

.ios-input:focus {
    outline: none;
    border-color: var(--ios-blue);
}

/* ==================== 聚合选项 ==================== */

.aggregation-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.aggregation-option {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.aggregation-option:hover {
    background: var(--ios-background-tertiary);
}

.aggregation-checkbox {
    margin: 0;
    width: 20px;
    height: 20px;
    accent-color: var(--ios-blue);
}

.aggregation-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--ios-text-primary);
    min-width: 120px;
}

.aggregation-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--ios-blue);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.aggregation-name {
    font-size: 14px;
}

.aggregation-description {
    flex: 1;
    font-size: 13px;
    color: var(--ios-text-secondary);
    line-height: 1.4;
}

/* ==================== 表单操作按钮 ==================== */

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.btn-secondary,
.btn-primary {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px; /* iOS最小触摸区域 */
}

.btn-secondary {
    background: var(--ios-background-tertiary);
    color: var(--ios-text-primary);
}

.btn-secondary:hover {
    background: var(--ios-background-quaternary);
}

.btn-secondary:active {
    transform: scale(0.98);
}

.btn-primary {
    background: var(--ios-blue);
    color: white;
}

.btn-primary:hover {
    background: var(--ios-blue-dark);
}

.btn-primary:active {
    transform: scale(0.98);
}

.btn-primary:disabled {
    background: var(--ios-background-quaternary);
    color: var(--ios-text-tertiary);
    cursor: not-allowed;
}

/* ==================== 快速操作 ==================== */

.quick-actions {
    display: flex;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid var(--ios-separator-color);
}

.btn-clear-all {
    background: var(--ios-background-tertiary);
    color: var(--ios-red);
}

.btn-clear-all:hover {
    background: rgba(255, 59, 48, 0.1);
}

.btn-apply-aggregations {
    background: var(--ios-green);
    color: white;
}

.btn-apply-aggregations:hover {
    background: var(--ios-green-dark);
}

/* ==================== 响应式设计 ==================== */

@media (max-width: 768px) {
    .aggregation-panel {
        width: 100vw;
        max-width: 100vw;
    }
    
    .aggregation-content {
        padding: 16px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .quick-actions {
        flex-direction: column;
    }
}

/* ==================== 聚合状态指示器 ==================== */

.aggregation-indicator {
    display: none;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: var(--ios-blue);
    color: white;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 8px;
}

.aggregation-indicator .indicator-dot {
    width: 6px;
    height: 6px;
    background: currentColor;
    border-radius: 50%;
    opacity: 0.8;
}

.aggregation-indicator .indicator-text {
    white-space: nowrap;
}

/* ==================== 动画效果 ==================== */

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.aggregation-panel.entering {
    animation: slideInFromRight 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.aggregation-panel.leaving {
    animation: slideOutToRight 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
